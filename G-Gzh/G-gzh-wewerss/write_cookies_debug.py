import urllib.parse
import sys
import os  # 添加os模块导入
from mitmproxy import ctx, http
import requests
import json

"""
终端运行:
mitmdump -s write_cookies.py -w dat mp.weixin.qq.com/mp/getappmsgext
只有手机端绑定访问: 才能截取到
需要获取请求头信息和cookie信息
"""

class WriterCookie:
    """
    mitmproxy的监听脚本，写入cookie和url到文件
    """

    def __init__(self) -> None:
        pass

    def response(self, flow: http.HTTPFlow) -> None:
        """
        完整的response响应
        :param flow: flow实例，
        """
        url = urllib.parse.unquote(flow.request.url)
        if "https://mp.weixin.qq.com/mp/getappmsgext" in url:
            print('getappmsgext:',flow.response.content)
            
        if "mp.weixin.qq.com/s" in url:
            ctx.log.info(f"Processing URL: {url}")
            try:

                # 获取headers
                headers_list = []
                for key, value in flow.request.headers.items():
                    headers_list.append((key.decode('utf-8') if isinstance(key, bytes) else key,
                                      value.decode('utf-8') if isinstance(value, bytes) else value))
                
                print('headers:', headers_list)
                cookies_str = "; ".join([f"{key}={value}" for key, value in flow.request.cookies.items()])
                print('cookies_str:', cookies_str)
                
                # 获取请求参数
                if flow.request.query:
                    print('request_params:', dict(flow.request.query))
                if flow.request.content:
                    print('request_body:', flow.request.content)
                
                # 获取响应数据
                # if flow.response.content:
                    # print('response_content:', flow.response.content)
                
                # 获取响应cookie
                response_cookies = {}
                for key, value in flow.response.cookies.items():
                    response_cookies[key] = value
                print('response_cookies:', response_cookies)
                # 处理响应cookie，将CookieAttrs对象转换为字符串值
                cookies_rep_list = []
                for key, (value, attrs) in flow.response.cookies.items():
                    # 对于某些特殊cookie，保留其原始值
                    if key in ['appmsg_token', 'pass_ticket', 'wap_sid2']:
                        cookies_rep_list.append(f"{key}={value}")
                    else:
                        # 其他cookie直接使用值部分
                        cookies_rep_list.append(f"{key}={value}")
                
                cookies_rep_str = "; ".join(cookies_rep_list)
                print('cookies_rep_str:', cookies_rep_str)
                
                # 获取响应头
                response_headers = {}
                for key, value in flow.response.headers.items():
                    response_headers[key.decode('utf-8') if isinstance(key, bytes) else key] = \
                        value.decode('utf-8') if isinstance(value, bytes) else value
                print('response_headers:', response_headers)
                
                # 创建配置数据
                config_data = {
                    "headers": dict(headers_list),
                    "cookies": cookies_rep_str
                }
                
                # 将数据写入config.json文件
                current_dir = os.path.dirname(os.path.abspath(__file__))
                config_file_path = os.path.join(current_dir, 'config.json')
                try:
                    with open(config_file_path, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, ensure_ascii=False, indent=4)
                    ctx.log.info(f"配置已保存到 {config_file_path}")
                except Exception as e:
                    ctx.log.error(f"保存配置文件失败: {e}")
                
                # if 'appmsg_token' in cookies_str:
                #     url = 'http://127.0.0.1:8000/configs/1'  # 替换为实际的URL
                #     headers = {
                #         'Content-Type': 'application/json'
                #     }
                #     data = {
                #         "status": 0,
                #         "message": "已经重置token和headers",
                #         "appmsg_cookie": cookies_str,
                #         "headers": headers_list
                #     }
                #     print('data:',data)
                #     res = requests.put(url, headers=headers, json=data)
                #     ctx.log.info(f"响应结果: {res}")

            except Exception as e:
                ctx.log.error(f"Error writing to file: {e}")

# 第四个命令中的参数
# addons = [WriterCookie(sys.argv[4])]
addons = [WriterCookie()]

def main():
    """
    用于debug时直接启动代理服务器
    """
    from mitmproxy.tools.main import mitmdump
    
    # 设置监听参数
    args = [
        '-p', '8080',  # 设置端口
        '-s', __file__,  # 加载当前脚本
        'mp.weixin.qq.com/mp/getappmsgext'  # 过滤规则
    ]
    
    # 启动代理服务器
    mitmdump(args)

if __name__ == '__main__':
    main()