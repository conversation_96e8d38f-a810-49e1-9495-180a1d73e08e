import requests
import json
import time
import csv
from datetime import datetime

def get_window_product(last_buffer=""):
    url = "https://mp.weixin.qq.com/cgi-bin/windowproduct?action=get_windowproduct"
    
    data_json = {
        "base_req": {"action": "ListMpSelectionProducts"},
        "ext_info": json.dumps({
            "page_size": 10,
            "key_word": "",
            "last_buffer": last_buffer,
            "order_type": 2,
            "order": 1,
            "finder_product_status": 1,
            "need_no_sale": False,
            "use_cache": False,
            "from_other_shop_only": 1
        })
    }
    
    data = {
        "data": json.dumps(data_json),
        "token": "334096942",
        "lang": "zh_CN",
        "f": "json",
        "ajax": "1"
    }
    
    headers = {
        "Cookie": Cookie,
        "dnt": "1",
        "origin": "https://mp.weixin.qq.com",
        "priority": "u=1, i",
        "referer": "https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit&action=edit&reprint_confirm=0&timestamp=1729912353793&type=77&appmsgid=302977889&token=334096942&lang=zh_CN",
        "sec-ch-ua": '"Not?A_Brand";v="99", "Chromium";v="130"',
        "sec-ch-ua-mobile": "?0",
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "x-requested-with": "XMLHttpRequest"
    }
    
    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"An error occurred: {e}")
        return None

def parse_product_list(result):
    if 'ext_info' not in result:
        print("Error: 'ext_info' not found in the response")
        return None, None, []

    try:
        ext_info = json.loads(result['ext_info'])
        products = ext_info.get('products', [])

        if not products:
            print("No products found in the response")
            return None, None, []

        print(f"Found {len(products)} products:")
        parsed_products = []
        for product in products:
            print(f"\nProduct ID: {product['product_id']}")
            print(f"Title: {product['title']}")
            price = product['selling_price']/100
            print(f"Price: ¥{price:.2f}")
            print(f"Stock: {product['stock']}")
            print(f"Sales: {product['sales']}")
            print(f"Platform: {product['platform_name']}")
            
            commission_info = product['bside_info']['commission_info']
            commission_rate = commission_info['commission_rate'] / 10000 if commission_info['has_commission'] else 0
            print(f"Commission Rate: {commission_rate:.2f}%")
            
            # 计算佣金
            commission = price * (commission_rate / 100)
            print(f"Commission: ¥{commission:.2f}")

            parsed_products.append({
                'product_id': product['product_id'],
                'title': product['title'],
                'price': price,
                'stock': product['stock'],
                'sales': product['sales'],
                'platform': product['platform_name'],
                'commission_rate': commission_rate,
                'commission': commission  # 新增佣金字段
            })

        last_buffer = ext_info.get('last_buffer', '')
        continue_flag = ext_info.get('continue_flag', False)
        print(f"\nLast Buffer: {last_buffer}")
        print(f"Continue Flag: {continue_flag}")

        return last_buffer, continue_flag, parsed_products

    except json.JSONDecodeError:
        print("Error: Unable to parse 'ext_info' as JSON")
    except KeyError as e:
        print(f"Error: Missing key in the response: {e}")
    
    return None, None, []

def save_to_csv(products, filename):
    if not products:
        print("No products to save.")
        return

    fieldnames = ['product_id', 'title', 'price', 'stock', 'sales', 'platform', 'commission_rate', 'commission']

    with open(filename, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        for product in products:
            writer.writerow(product)

    print(f"Data saved to {filename}")

# Usage
Cookie='appmsglist_action_3094060680=card; noticeLoginFlag=1; remember_acct=347246463%40qq.com; ua_id=HczJejZZynhI2LhSAAAAAGGizoX3Zel3ni9WGB-DEGg=; mm_lang=zh_CN; cert=r5visS5hOaOZrR5ghyAWnJtdzUs9zxF2; _qimei_uuid42=18603112105100766258358396d220f571623068f6; _qimei_h38=bafbbe986258358396d220f50300000aa18603; _qimei_q36=; pgv_info=ssid=s5299955142; pgv_pvid=3728192808; logintype=1; pac_uid=8QIf3n5c6IcYsTrd4As=; backup_logintype=1; media_ticket=ebbbcf6f68f0e2406fb2cd4a310a774740ecf6d4; media_ticket_id=3094060680; RK=/LutUE3DYy; ptcz=a13fcf29b1b49c6ef45205f95db5c8d4f9996385d917ab2f2975b33d11176908; ETCI=ff3a60f6d4514bcba18a9be61d2f2d78; msecToken=b0acf4066fd6680973b8281431071b48; _gcl_au=1.1.938740187.1723296650; open_id=; skey=null; luin=null; lskey=null; user_id=null; session_id=null; suid=user_8QIf3n5c6IcYsTrd4As%3D; qq_domain_video_guid_verify=51ab986a17ed4387; tvfe_boss_uuid=1c1c0e65c65e343b; vversion_name=8.2.95; video_omgid=51ab986a17ed4387; uin=o347246463; rewardsn=; wxtokenkey=777; wxuin=27400818432879; ts_uid=5252084588; _qimei_fingerprint=850e811ad71ae7d8ed2400e5d10d7c04; news_token=EoABKAO4Qrjv_FPYHRFT-kbv0oZnuhmZP4wpbyfc_N8_HuDdUuoYOf6HrMko60Q8s7-b_Z4o6bzy7ZpRPmDM219_ooTinPnM6O8pol-evFhp3NAkDwRMRUTzPQRy2V-VWrI9fjABF31hAy0xuo0D8MQlMulU7zIruvk3MeVdxjv4zSsgEQ; backup_news_token=EoABKAO4Qrjv_FPYHRFT-kbv0oZnuhmZP4wpbyfc_N8_HuDdUuoYOf6HrMko60Q8s7-b_Z4o6bzy7ZpRPmDM219_ooTinPnM6O8pol-evFhp3NAkDwRMRUTzPQRy2V-VWrI9fjABF31hAy0xuo0D8MQlMulU7zIruvk3MeVdxjv4zSsgEQ; poc_sid=HObIEWejSen1JXzukP4qCieFzRf0niRl8EI9sUzC; mp_token=1710611026; sess_skey=f71426e96ec1d0aedfd4882df9e637ab; login_type=3; nick_name=%E4%BC%BC%E6%B0%B4%E4%BC%BC%E6%B5%81%E5%B9%B4; head_img=http%3A%2F%2Fwx.qlogo.cn%2Fmmopen%2FEnl5UQjdE7Lz2CaCF44EPgsD8eVHD9L1OM4gmuEDPB30YbvHGnNxZZBFZwbV4E1MTyq3r72bliaWEg2mYiaDnQgAZhBJp4FD2L%2F0; login_level=undefined; lbs_refer=9148bf92cf751e8b928c9e894b2d77c5a0d0918277cdb7541aab39bdddadeb91d28e5e36be439643259a0b99d14c9e0111d121ae253378adb9edf94a71dbfb5a133be1a9c80d8addf28f8fa5331f6ca4; mmad_session=7e2e8f52cbd87280ffa4c90e9e2d0f81788bfab2e619a92f0a704e580c1c7dcbdd097f5a9e566a75c04fec27cffdb786a7677dae00c45814139464f2a7711397c368e9314fd5dd2e540ce0e02b497149fb786afa7ef889310f70ada1ac2c17c211de1c56c245721266e7088080fefde3; noticeLoginFlag=1; openid2ticket_o9XSQ7XsrVpEXFeHO1yFRRdTyEUo=52wN3h5AEEWFOlYT/Sad+KS59BmeWSo16AyNm+50y9w=; sig=h0189d1ba70e42bf3b8dcf1ac4894182ea1a776b981d06a12c139aacbc5fc9c59ed7f7e60de0ecfa48b; uuid=c6d76c3bfc3cd903c85cf1c4d409cc57; bizuin=3094060680; ticket=920e8d1b332e1214830e7927a709ab4f38bc89af; ticket_id=gh_d2247b90ba00; slave_bizuin=3094060680; remember_acct=347246463%40qq.com; rand_info=CAESIPG9zOp2Dip2QFlfOpkzK9hR8MVCeE2kMNZHVDaq20Ih; data_bizuin=3094060680; data_ticket=FixO581pLZW5/X6qt0y8i4klWW6LVsi28NEg8qKLhXd5RCbUaVMYT3epQYQmjQQ0; slave_sid=TVFRUnNwYURkUTJFeDFnWlZhNHZ0bkJTbG9xQkRYNmozTlVRTmF6bUN1VjZjNHVYc1RNZXpDWldUV2xWbk1YUm5kQXRxdGFGb3lNQXNEa2lIYXVnRzNTenpybWU4cHR6U1VmMHpWMlUxTlFsZlNZUGk1UnJaNG5QU0VJUUsydDFyY3ZjeFZRdmxic0NseGhU; slave_user=gh_d2247b90ba00; xid=887461711a1010797e4b9feff89bfb44; openid2ticket_oiJ5ruPKq527KNc4PzfuWXJkFk5o=1igGtgROX4uxQ1DM6qty8zaEzpsl2/bHDgj4KJPm4l8=; _clck=3094060680|1|fqc|0; _clsk=i23um3|1729913457152|15|1|mp.weixin.qq.com/weheat-agent/payload/record'

if __name__ == "__main__":
    """联盟商品列表爬取"""
    last_buffer = ""
    products_dict = {}  # 使用字典来存储产品，以product_id为键
    for i in range(100):  # 循环10次
        time.sleep(5)
        print(f"\n--- Request {i+1} ---")
        result = get_window_product(last_buffer)
        if result:
            if result['base_resp']['ret'] == 0:
                last_buffer, continue_flag, products = parse_product_list(result)
                # 更新products_dict，如果product_id已存在，则更新信息
                for product in products:
                    products_dict[product['product_id']] = product
                if not continue_flag:
                    print("No more data to fetch.")
                    break
            else:
                print(f"Error: {result['base_resp']['err_msg']}")
                break
        else:
            print("Failed to get window product data")
            break

    # 将字典转换回列表
    all_products = list(products_dict.values())

    # 按佣金倒序排序
    all_products.sort(key=lambda x: x['commission'], reverse=True)

    # 生成带有时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"window_products_{timestamp}.csv"
    
    # 保存数据到CSV文件
    save_to_csv(all_products, filename)

    # 打印排序后的前5个产品（如果有的话）
    print(f"\nTotal unique products: {len(all_products)}")
    print("\nTop 5 products by commission:")
    for product in all_products[:5]:
        print(f"Title: {product['title']}, Commission: ¥{product['commission']:.2f}")
