import os
import re
import time
from dotenv import load_dotenv
import requests
# from llm import llm_poe
from llm.llm_groq_tiyu import call_content_common
from util import article_util
from util import notion_util
from util import system_util
from util import re_util
from util import gzh_parse_util
from util import  article_util, re_util,system_util,markdown_html_util
from util import notion_util,downimg_util,gzh_api
load_dotenv()


def general_yuer(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    try:
        contents = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
        if contents is None or len(contents) < 500: return False

        content = re.sub(r'^.*?一、', '一、', contents, flags=re.DOTALL)  # 去除一\前面冗余内容
        parts = re_util.parse_text(text=content)
        # 打印解析后的三个部分
        for i, part in enumerate(parts, 1):
            title, content = re_util.handle_content(text=part)
            # new_weixin_title = re.sub(r'[^\w\s,，、!！？？]', '', title)
            new_weixin_title = re.sub(r'^[一二三四五六七八九十]、', '', title)
            # 转换成word文档
            os_type = system_util.get_os()
            output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
            at = article_util.article()
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\风景夕阳' if os_type == "Windows" else f'/Volumes/文章存档/Images/风景夕阳'
            at.create_word_doc_content_yuer(file_path=output_docx, contents=content, temp_dir=temp_dir)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
    return False


def run_notion():
    """通过Notion获取数据"""
    try:
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 10000,
            'author': '谷雨妈妈',  # 三好老妈  谷雨妈妈
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

        result = general_yuer(page_title=page_title, page_url=page_url, area='育儿')
        if result:
            notion.update_page_content(page_id=page_id, properties_params='格式化')
        else:
            notion.update_page_content(page_id=page_id, properties_params='生成失败')
    except Exception as e:
        print(f"育儿异常:{e}")

class General:
    def __init__(self) -> None:
        print('初始化类')
        pass

    def _01_get_data(self):
        """从Notion获取数据-公众号文章URL"""
        try:
            """筛选条件:领域+点赞+时间"""
            params = {
                'readcount': 2000,
                # 'author': '谷雨妈妈',  # 三好老妈  谷雨妈妈
                'area':'yuer'
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None: return None,None
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')
            
            flag=True
            notion.update_page_properties(page_id=page_id,tags='格式化' if flag else "发布失败", area='育儿')
            # notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")
            return page_url,page_id
        
        except Exception as e:
            print(f"育儿异常:{e}")
        return None,None
    
    def _02_parse_data(self,ghzArticleUrl):
        try:
            return gzh_parse_util.get_weixin_content(ghzArticleUrl)
        except Exception as e:
            print(f'[异常]无法解析网址: {e}')
        return None,None,None

    def _03_llm_content(self,contentText):
        content_llm=self.summarize_with_groq(contentText)
        return content_llm

    def summarize_with_groq(self,content):
        """使用Groq总结文章中心思想和提取图片"""
        try:
            return call_content_common(content,area)
        except Exception as e:
            print(e)
        return None
    
    def _04_gzh_upload_image(self):
        return
    
    def _05_general_html(self, title: str, content: str, file_path: None, image_urls: list):
        # 校验并过滤图片URL
        valid_image_urls = []
        for img_url in image_urls:
            try:
                # Filter out URLs containing 'wx_fmt=gif' or 'wx_fmt=other'
                if 'wx_fmt=gif' in img_url or 'wx_fmt=other' in img_url or 'sz_mmbiz_png' in img_url:
                    print(f"排除不支持的图片格式: {img_url}")
                    continue
                # Ensure the URL starts with 'https://'
                if not img_url.startswith('https://'):
                    continue
                # 使用requests库检查URL是否可访问
                response = requests.head(img_url, timeout=5)
                if response.status_code == 200:
                    valid_image_urls.append(img_url)
                else:
                    print(f"排除无效的图片URL: {img_url}, 状态码: {response.status_code}")
            except requests.RequestException as e:
                print(f"无法访问图片URL: {img_url}, 错误: {str(e)}")
        
        # 更新image_urls为有效的URL列表   #路径gif去除, http去除 wx_fmt=gif other 去除.
        image_urls = valid_image_urls

        try:
            new_image_list=[]
            for idx, img_url in enumerate(image_urls):
                image_down_path=downimg_util.down_image_from_url(url=img_url,save_path=global_image_save_path)
                gzh_save_path=client.upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)
                # 首张图片-上传封面
                if idx == len(image_urls) - 1:
                    thumb_media_id=client.upload_cover_image(image_down_path)
            # TODO: 图片从新的图片库中获取

            

            # 生成html文档
            file_path = f'{file_path}{title}.html'
            markdown_html_util.markdown_to_html(content,file_path,new_image_list)
            
            # 组装articles
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            article={
                'title':title,
                'author':global_author,
                'content':content,
                'thumb_media_id':thumb_media_id
            }
            return article
        except Exception as e:
            print(f'生成文档失败:{e}')
        return None

    def _06_gzh_create_draft(self,articles):
        # 3. 插入公众号草稿
        media_id=client.create_draft(articles)
        print(media_id)
        return media_id
    
    def _07_gzh_publish(slef,media_id):
        return client.publish_article(media_id)

        


def run():
    """
    1. 获取数据源
    2. 解析数据图片和内容
    3. AI生成内容
    4. 上传图片
    5. 整理图片和内容生成HTML
    6. 创建草稿
    """
    articles=[]
    pageIds=[]
    pageFailIds=[]
    general=General()
    count = 0
    while True:
        docx_files = [f for f in os.listdir(global_destination_folder) if f.endswith('.html')]
        count += 1
        if count > 10: break  # 限定二十次
        if len(docx_files) < 3:  # 文件小于4篇持续
            page_url,page_id=general._01_get_data()
            if page_url is None: continue
            try:
                weixin_title,text_content,image_urls=general._02_parse_data(page_url)
                if weixin_title is None:
                    notion.update_page_properties(page_id=page_id,tags='内容失效',area='育儿')
                    continue
            except Exception as e:
                print(f'[异常]解析地址失败:{e}')
                continue
        

            content_llm=general._03_llm_content(text_content)
            article=general._05_general_html(weixin_title,content_llm,global_destination_folder,image_urls)
            if article is None:
                pageFailIds.append(page_id)
                continue
            articles.append(article)
            pageIds.append(page_id)
        else:
            print(f"{global_destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again

    
    try:
        media_id=general._06_gzh_create_draft(articles)
        print(f"[成功]创建草稿，media_id: {media_id}")
    except Exception as e:
        print(f"[失败]创建草稿: {e}")
        media_id = None
            
    # 草稿创建失败回滚
    if media_id is None:
        for page_id in pageIds:
            if page_id:
                try:
                    # notion.update_page_content(page_id=page_id, properties_params="初始化")
                    notion.update_page_properties(page_id=page_id,tags='初始化', area='yuer')
                except Exception as e:
                    print(f"[回滚]Notion页面状态失败 (page_id: {page_id}): {e}")
    # 失败的id回滚
    for page_id in pageFailIds:
        try:
            # notion.update_page_content(page_id=page_id, properties_params="初始化")
            notion.update_page_properties(page_id=page_id,tags='初始化', area='yuer')
        except Exception as e:
            print(f"[回滚]Notion页面状态初始化 (page_id: {page_id}): {e}")
        
    # general._07_gzh_publish(media_id)

    # 删除所有的HTML文件
    for f in os.listdir(global_destination_folder):
        if f.endswith('.html'):
            os.remove(os.path.join(global_destination_folder, f))
    print('[成功]:清理文件')

"""全局配置"""
area='yuer'

global_destination_folder = "/Volumes/文章存档/育儿/待发布/"  # 目标文件夹
global_image_save_path=f'/Volumes/文章存档/Images/公众号/育儿/' #体育图片存储文件夹
global_author='西兰妈妈'

NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)


YUER_XILAN_WECHAT_APP_ID = os.getenv('YUER_XILAN_WECHAT_APP_ID')
YUER_XILAN_WECHAT_APP_SECRET = os.getenv('YUER_XILAN_WECHAT_APP_SECRET')

YUER_MAODOU_WECHAT_APP_ID = os.getenv('YUER_MAODOU_WECHAT_APP_ID')
YUER_MAODOU_WECHAT_APP_SECRET = os.getenv('YUER_MAODOU_WECHAT_APP_SECRET')
# client=gzh_api.WECHATCLIENT(YUER_XILAN_WECHAT_APP_ID,YUER_XILAN_WECHAT_APP_SECRET)

def main():
    global client
    for i in range(2):
        # i+=1 #直接第二个账号
        if i==0:
            client=gzh_api.WECHATCLIENT(YUER_XILAN_WECHAT_APP_ID,YUER_XILAN_WECHAT_APP_SECRET)
        elif i==1:
            client=gzh_api.WECHATCLIENT(YUER_MAODOU_WECHAT_APP_ID,YUER_MAODOU_WECHAT_APP_SECRET)
        
        for f in os.listdir(global_destination_folder):
            if f.endswith('.html'):
                os.remove(os.path.join(global_destination_folder, f))
        run()

if __name__ == "__main__":
    """育儿-西兰-毛豆"""
    # run_notion() #方式一
    # gzh_article_clean_tools.clean()
    
    main() # 方式二
    print('程序结束...')
  

