
import time
import schedule
from gzh_html_tiyu_news import main as tiyu_main
from gzh_tiyu_feed import main as tiyu_feed_main
from gzh_html_touxiang import main as touxiang_main
# from gzh_html_jiaoyu import main as jiaoyu_main
# from gzh_html_yuer import main as yuer_main

def job():
    print('[程序运行时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    tiyu_main()
    touxiang_main()
    # jiaoyu_main()
    # yuer_main()
    tiyu_feed_main() #获取体育数据
    print('[程序结束时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    schedule.every().day.at("07:00").do(job)  # 指定时间触发
    # schedule.every().day.at("15:00").do(job)  # 指定时间触发
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[公众号文章生成:程序启动...]')
    # schedule_job() #定时运行
    job() #直接运行
