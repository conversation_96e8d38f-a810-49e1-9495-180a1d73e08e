from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches, Pt
import os
from pathlib import Path
import requests
import tempfile

def download_image(url):
    """下载图片并返回临时文件路径"""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            tmp_file.write(response.content)
            return tmp_file.name
    except Exception as e:
        print(f"下载图片失败: {url}")
        print(f"错误信息: {str(e)}")
        return None

def html_to_word(html_path):
    # 读取HTML文件
    with open(html_path, 'r', encoding='utf-8') as file:
        html_content = file.read()
    
    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 创建Word文档
    doc = Document()
    
    # 获取文章标题
    title = soup.find('title')
    if title:
        doc.add_heading(title.text, 0)
    
    # 处理正文内容
    content = soup.find('body')
    if content:
        # 遍历所有段落、标题和图片
        for element in content.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img']):
            # 处理标题 
            if element.name.startswith('h'):
                level = int(element.name[1])
                doc.add_heading(element.text.strip(), level)
            
            # 处理图片
            elif element.name == 'img':
                img_url = element.get('src', '')
                if img_url.startswith('http://mmbiz.qpic.cn'):
                    img_path = download_image(img_url)
                    if img_path:
                        try:
                            doc.add_picture(img_path, width=Inches(6))  # 设置图片宽度为6英寸
                            # 删除临时图片文件
                            os.unlink(img_path)
                        except Exception as e:
                            print(f"插入图片失败: {str(e)}")
            
            # 处理段落
            else:
                text = element.text.strip()
                if text:  # 只添加非空段落
                    paragraph = doc.add_paragraph(text)
                    # 设置段落格式
                    paragraph_format = paragraph.paragraph_format
                    paragraph_format.space_after = Pt(12)
                    paragraph_format.line_spacing = 1.15
    
    # 生成输出文件路径
    output_dir = os.path.dirname(html_path)
    output_filename = Path(html_path).stem + '.docx'
    output_path = os.path.join(output_dir, output_filename)
    
    # 保存Word文档
    doc.save(output_path)
    return output_path

if __name__ == "__main__":
    html_path = '/Volumes/文章存档/热点/待发布/惊天逆转！女教师出轨学生，丈夫曝光反被告侵犯隐私？.html'
    output_path = html_to_word(html_path)
    print(f"Word文档已保存至: {output_path}")