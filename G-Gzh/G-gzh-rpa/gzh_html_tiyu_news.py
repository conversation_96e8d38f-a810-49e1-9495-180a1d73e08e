import requests
import json,time
from llm.llm_groq_tiyu import call_content_common
from wechatpy import WeChatClient

from bs4 import BeautifulSoup
import re
from util import  article_util, re_util,markdown_html_util
from util import notion_util,feed_util,time_util,downimg_util,gzh_api,watermark_util
import requests
from requests.adapters import HTTPAdapter
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from dotenv import load_dotenv
import os
load_dotenv()

class TiYu:
    def __init__(self):
        print("TiYu init start")


    def _get_general_word(self, title: str, content: str, file_path: None, image_urls: list):
        try:
            at = article_util.article()
            print('生成Word中...')
            # 方式一: 生成docx文档
            file_path = f'{file_path}{title}.docx'
            at.create_word_doc_content_tiyu(file_path=file_path, contents=content, image_urls=image_urls)
            # at.create_html_content_tiyu(file_path=file_path, contents=content, image_urls=image_urls)

            # 方式二: 生成HTML
            # 图片URL转换成公众号的URL:
            # 先下载 再上传 
            new_image_list=[]
            for idx, img_url in enumerate(image_urls):
                image_down_path=downimg_util.down_image_from_url(url=img_url,save_path=global_image_save_path)
                gzh_save_path=client.upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)

                # 首张图片-上传封面
                if idx==0:
                    thumb_media_id=client.upload_cover_image(image_down_path)

            
            #TODO: 上传封面获取ID, 创建草稿接口,发布接口统一整理- 实现上传两篇文章
            file_path = f'{global_destination_folder}{title}.html'
            markdown_html_util.markdown_to_html(content,file_path,new_image_list)

            # 组装articles
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        
            article={
                'title':title,
                'author':global_author,
                'content':content,
                'thumb_media_id':thumb_media_id
            }
            return article
        except Exception as e:
            print(f'生成文档失败:{e}')
        return None




def get_news_list(url, max_retries=3):
    """获取腾讯主页新闻列表"""
    session = requests.Session()
    retry = Retry(total=max_retries, backoff_factor=0.1)
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    try:
        response = session.get(url, timeout=10)
        response.raise_for_status()  # 如果状态码不是200，将引发异常
        data = response.json()
        news_list = []
        
        # 解析JSON数据,提取id、url和标题
        if 'newslist' in data:
            for item in data['newslist']:
                news_item = {
                    'id': item.get('id'),
                    'title': item.get('title'),
                    'url': item.get('url'),
                    'comments': item.get('comments'),
                    'image_url': item.get('thumbnails_big')[0],
                    'time': item.get('time')
                }
                news_list.append(news_item)
        
        return news_list
    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {e}")
        return None

def get_article_content(url):
    """获取腾讯文章详细内容"""
    response = requests.get(url)
    script_content=response.text
    # 使用正则表达式提取JSON内容
    match = re.search(r'window\.DATA\s*=\s*(\{.*?\});', script_content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            data = json.loads(json_str)
            text_content = data['originContent']['text']
            # 使用BeautifulSoup解析HTML内容
            soup = BeautifulSoup(text_content, 'html.parser')
            all_text = soup.get_text(separator='\n')
            # print(all_text)

            # 提取originAttribute中的列表-图片列表
            origin_attribute_dict = data.get('originAttribute', [])
            origurl_objects = []
            for key, value in origin_attribute_dict.items():
                if 'origUrl' in value:
                    origurl_objects.append(value['origUrl'])
            
            # print(origurl_objects)

            return {
                # 'title': title_text,
                'text': all_text,
                'images': origurl_objects,
                'links': url
            }

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
    else:
        print("未找到JSON内容")
    return None


def summarize_with_groq(content):
    """使用Groq总结文章中心思想和提取图片"""
    try:
        return call_content_common(content,area)
    except Exception as e:
        print(e)
    return None
    

def generate_article_with_poe(content):
    """用Poe生成新文章"""
   


def create_word_document(content, images):   
    """生成Word文档"""
    if content is None or len(content) == 0: return False
    title, content = re_util.handle_content(text=content)
    title = re.sub(r'[^\w\s,，、!！？？]', '', title)
    title=title.strip()

    # 4.生成word文档:
    ty = TiYu()
    return ty._get_general_word(title=title, content=content, file_path=global_destination_folder, image_urls=images)


def general_word_auto():
    """自动生成文章-llm大模型"""
    try: 
        # 1.检索Notion条件数据
        day = time_util.get_yesterday(2)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'area': area,
            'datasource': 'i.news.qq.com'
        }
        contentDict = notion.get_content_by_condition_nba(params)
        if contentDict is None: 
            print(f'Notion没有对应的数据:{params}')
            return None,None
        page_id = contentDict['page_id']
        page_chinese_title = contentDict['page_chinese_title']
        page_url = contentDict['page_url']
        print(f'待处理: page_id:{page_id},page_chinese_title:{page_chinese_title},page_url:{page_url}')
        # 2.解析原始文章内容
        content = get_article_content(page_url)
        # 3. 大模型生成文章内容
        content_llm=summarize_with_groq(content['text'])
        print(f'content_llm:{content_llm}')
        print(f'content_llm_len:{len(content_llm)}')
        if content_llm is None: 
            print(f'大模型没有生成内容:{contentDict}')
            return None,page_id
        # 4. 生成docx文档
        images=content['images']
        article=create_word_document(content_llm, images)
        if article is None: 
            flag=False
        else:
            flag=True
        # 5. 更新Notion数据状态
        notion.update_page_properties(page_id=page_id,tags='格式化' if flag else "发布失败", area='tiyu')
        return article,page_id
    except Exception as e:
        print(f'生成文档异常: {e}')
    return None,page_id


def save_notion():
    # 1. 获取新闻用户主页最新数据
    ids = feed_util.get_exist_ids('ids-newqq.json')
    for url in global_source_urls:    
        contents = get_news_list(url)
        if contents is None or len(contents) == 0: continue
        for content in contents:
            if content.get('id') in ids: continue
            if content.get('image_url') is None: continue
            image_url = content['image_url']
            if image_url is None or len(image_url) == 0: continue
            title=content['title']
            datasource = 'i.news.qq.com'
            url=content['url']
            id=content['id']
            # 保留字段,目前不用!
            text_content = f"""你是一位擅长创作病毒式传播内容的资深文案大师。你的任务是将给定的文本改写成极具传播力的版本。请遵循以下指南：

1. 保留原文的核心主题和关键信息。
2. 使用更加吸引眼球的表述和强烈的情感表达，特别是开头部分。
3. 增强内容的口语化魅力和情感冲击力。
4. 输出限制在150字以内。
5. 最后一行与读者互动，提出问题或呼吁行动。
6. 在文末添加2-3个相关话题标签。

输出格式：
[改写后的内容，包括互动句]

#[标签1] #[标签2] #[标签3]

注意：
- 只输出改写后的内容，不要包含任何解释或元信息。
- 使用简体中文。
- 适当断行以增强可读性和感染力。
- 不要使用引号或其他特殊格式。

请基于以上要求，将我接下来提供的文本改写成病毒式传播内容。 内容链接是：{url}"""

            # # 6. 记录到Notion中
            page = {
                "title": title,
                'chinese_title': title,
                'prompt': text_content,
                'id': id,
                'area': 'tiyu',
                'datasource': datasource,
                'hmcturl': content['url'],
                'published_at': content['time'],
                'picture_url': image_url
            }
            try:
                newPage = notion.create_page(page=page)
                page_id = newPage['id']
                print(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')
                feed_util.save_ids(title, id, 'success','ids-newqq.json')
            except Exception as e:
                print(e)
                continue


def get_meinv_article():
    """
    获取美女文章内容并处理
    """
    MAX_RETRIES = 3
    RETRY_DELAY = 5  # seconds

    for attempt in range(MAX_RETRIES):
        try:
            from gzh_meinv import get_meinv_content
            result = get_meinv_content()
            if result is None:
                raise ValueError("get_meinv_content 返回了 None")
            
            title, pic_list,page_id = result
            if not title or not pic_list:
                raise ValueError("标题或图片列表为空")

            new_image_list = []
            thumb_media_id = None

            for idx, img_url in enumerate(pic_list):
                try:
                    if img_url.startswith('http://'): continue  # 作者头像图片不要
                    save_path=f'{global_image_fuli_save_path}{title}/'
                    if not os.path.exists(save_path):
                        os.makedirs(save_path)
                    image_down_path = downimg_util.down_image_from_url(url=img_url, save_path=save_path)
                    watermark_util.handle_image(image_down_path)
                    gzh_img_url = client.upload_article_image(image_down_path)
                    
                    if gzh_img_url:
                        new_image_list.append(gzh_img_url)
                        
                        if idx == len(pic_list) - 2: #注意封面图片-剔除的数据要保证不异常
                            thumb_media_id = client.upload_cover_image(image_down_path)
                except Exception as e:
                    print(f"处理图片时出错 {img_url}: {e}")
                    continue

            if not new_image_list:
                raise ValueError("没有成功上传任何图片")

            file_path = f'{global_destination_folder}{title}.html'
            content = ""
            markdown_html_util.markdown_to_html_with_images(content, file_path, new_image_list)

            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            if thumb_media_id is None:
                notion.update_page_content(page_id=page_id, properties_params="初始化")
                return None

            return {
                'title': title,
                'author': global_author,
                'content': content,
                'thumb_media_id': thumb_media_id
            }

        except Exception as e:
            print(f"获取美女文章失败，第 {attempt + 1} 次尝试: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_DELAY)
            else:
                print(f"获取美女文章失败，已重试 {MAX_RETRIES} 次")
                return None

    return None  # 如果所有尝试都失败，返回 None

def run():
    try:
        # 1. 保存所有Rss数据到Notion
        save_notion()
        # 2. 循环判断生成文章数量
        count = 1
        articles = []
        pageIds = []
        while True:
            docx_files = [f for f in os.listdir(global_destination_folder) if f.endswith('.html')]
            if len(docx_files) < max_article_num:  # 文章限制生成数量
                if count > 5: 
                    print(f'重试次数超过6次,终止进程')
                    break
                article, page_id = general_word_auto()
                if page_id:
                    pageIds.append(page_id)
                
                if article is not None and article.get('thumb_media_id') is not None:
                    articles.append(article)
                count += 1
            else:
                print(f"目录:{global_destination_folder},html文件数量为:{len(docx_files)}, 大于或等于3篇")
                break
            time.sleep(1)  # Wait for 1 second before checking again
        
        # 判断是否有文章数据
        if len(articles) == 0:
            for page_id in pageIds:
                if page_id:
                    try:
                        notion.update_page_content(page_id=page_id, properties_params="初始化")
                    except Exception as e:
                        print(f"更新Notion页面状态失败 (page_id: {page_id}): {e}")
            return
        
        # 增加两篇福利美女Html
        for i in range(2):
            try:
                article = get_meinv_article()
                if article is not None and article.get('thumb_media_id') is not None:
                    articles.append(article)
            except Exception as e:
                print(f"第{i+1}次获取美女文章失败: {e}")

        # 3. 插入公众号草稿
        try:
            media_id = client.create_draft(articles)
            print(f"[成功]创建草稿，media_id: {media_id}")
        except Exception as e:
            print(f"创建草稿失败: {e}")
            media_id = None
        
        # 草稿创建失败回滚
        if media_id is None:
            for page_id in pageIds:
                if page_id:
                    try:
                        notion.update_page_content(page_id=page_id, properties_params="初始化")
                    except Exception as e:
                        print(f"更新Notion页面状态失败 (page_id: {page_id}): {e}")
        
        
    except Exception as e:
        print(f"体育异常:{e}")
        #异常: Notion数据状态回滚:
        for page_id in pageIds:
            if page_id is None: continue
            try:
                notion.update_page_content(page_id=page_id, properties_params="初始化")
            except Exception as inner_e:
                print(f"回滚状态时发生错误 (page_id: {page_id}): {inner_e}")

def create_meinv():
    '''单独方法增加福利'''
    articles = []
    try:
        article = get_meinv_article()
        if article is not None:
            articles.append(article)
    except Exception as e:
        print(f"获取美女文章失败: {e}")

    # 3. 插入公众号草稿
    try:
        if len(articles)>0:
            media_id = client.create_draft(articles)
            print(f"[成功]创建草稿，media_id: {media_id}")
    except Exception as e:
        print(f"美女创建草稿失败: {e}")
        media_id = None

"""全局配置"""
max_article_num=2
area='tiyu'
global_author='翰墨体育'
global_destination='/Volumes/文章存档/体育/'
global_destination_folder='/Volumes/文章存档/体育/待发布/' #检索目标文件夹
global_image_save_path=f'/Volumes/文章存档/Images/公众号/体育/' #体育图片存储文件夹
global_image_fuli_save_path=f'/Volumes/文章存档/Images/公众号/福利/' #体育图片存储文件夹
global_source_urls = [
    # "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QIf3n9d6oIcsDja5gM%3D&tabId=om_article&caller=1&from_scene=103",  # 大秦壁虎
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMf3ndf7YAYuD7a&tabId=om_article&caller=1&from_scene=103",  # 追球者
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMW3Htd5IIevz8=&tabId=om_article&caller=1&from_scene=103",  # 醉卧浮生
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb33hb64YZvD0=&tabId=om_article&caller=1&from_scene=103",  # 诗话篮球
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb2Hld64Mf&tabId=om_article&caller=1&from_scene=103",  # 罗说NBA
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa33dZ6YEfvDY=&tabId=om_article&caller=1&from_scene=103",  # 颜小白的篮球梦
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa2Xde6IMfuDc%3D&tabId=om_article&caller=1&from_scene=103"  # 篮球教学论坛
]
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)

TIYU_HANMO_WECHAT_APP_ID = os.getenv('TIYU_HANMO_WECHAT_APP_ID')
TIYU_HANMO_WECHAT_APP_SECRET = os.getenv('TIYU_HANMO_WECHAT_APP_SECRET')

TIYU_MAODOU_WECHAT_APP_ID = os.getenv('TIYU_MAODOU_WECHAT_APP_ID')
TIYU_MAODOU_WECHAT_APP_SECRET = os.getenv('TIYU_MAODOU_WECHAT_APP_SECRET')



def main():
    """运行须知:
    1. 需要VPN,确保能正常打开groq网站;香港不行-需要国外
    2. 需要打开Windows挂载文件夹
    3. 翰墨体育+毛豆体育
    """
    try:
        clientVer = WeChatClient(TIYU_HANMO_WECHAT_APP_ID, TIYU_HANMO_WECHAT_APP_SECRET)
        token = clientVer.access_token
        print(f'[成功]TIYU_HANMO_WECHAT_APP_ID: {token}')
        clientVer = WeChatClient(TIYU_MAODOU_WECHAT_APP_ID, TIYU_MAODOU_WECHAT_APP_SECRET)
        token = clientVer.access_token
        print(f'[成功]TIYU_MAODOU_WECHAT_APP_ID: {token}')
    except Exception as e:
        print(f'[失败]token失效:{e}')
        return

    global client
    for i in range(2):
        # i+=1 #直接第二个账号
        if i==0:
            client=gzh_api.WECHATCLIENT(TIYU_HANMO_WECHAT_APP_ID,TIYU_HANMO_WECHAT_APP_SECRET)
        elif i==1:
            client=gzh_api.WECHATCLIENT(TIYU_MAODOU_WECHAT_APP_ID,TIYU_MAODOU_WECHAT_APP_SECRET)
        # 删除所有的HTML文件- 保留docx用于上传头条
        for f in os.listdir(global_destination_folder):
            if f.endswith('.html'):
                os.remove(os.path.join(global_destination_folder, f))
        print('[成功]:清理文件')

        # 移动docx文件到历史文件夹
        history_folder = os.path.join(global_destination, "历史文章")
        if not os.path.exists(history_folder):
            os.makedirs(history_folder)
            
        for f in os.listdir(global_destination_folder):
            if f.endswith('.docx'):
                src_path = os.path.join(global_destination_folder, f)
                dst_path = os.path.join(history_folder, f)
                try:
                    os.rename(src_path, dst_path)
                except Exception as e:
                    print(f"移动文件失败 {f}: {e}")
        print('[成功]:移动docx文件到历史文件夹')

        # 运行
        run()
    
if __name__ == "__main__":
    main()
    print('程序结束...')