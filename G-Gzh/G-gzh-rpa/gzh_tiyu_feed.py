import os
from dotenv import load_dotenv

from llm import llm_poe
from util import feed_util
from util import notion_util
from util import time_util
from util import transaction_tengxun_util

load_dotenv()

class TiYu:
    def __init__(self):
        print("TiYu init start")

    def _init_params(self):
        print("TiYu init start")
        content = self._get_news()
        self.title = content['title']
        self.page_url = content['link']
        self.id = content['id']
        self.published_at = time_util.formatted_date(content['published'])
    def _get_news_list(self):
        espn_contents = feed_util.check_feed_espn_list()
        contents = feed_util.check_feed_bleacherreport_list()
        if espn_contents is None and contents is None: return None
        espn_contents.extend(contents)
        return espn_contents



def save_notion():
    # # 1. 获取订阅Rss最新数据
    ty = TiYu()
    contents = ty._get_news_list()
    if contents is None or len(contents) == 0: return
    for content in contents:
        if content.get('image_url') is None: continue
        
        image_url = content['image_url']
        if image_url is None or len(image_url) == 0: continue
        if 'bleacherreport.com' in content['link']:
            datasource = 'bleacherreport.com'
            ty.published_at = content['published']
        elif 'espn.com' in content['link']:
            datasource = 'espn.com'
            ty.published_at = time_util.convert_est_to_cst(content['published'])
        ty.page_url = content['link']
        ty.id = content['id']
        ty.title = content['title']
        title = transaction_tengxun_util.translate_text(ty.title)
        print(f'\n中文标题:{title}')

        # 保留字段,目前不用!
        # text_content = f'[{ty.page_url}]联网检索相关信息，保证信息的准确性！重新生成新闻文章,保证标题吸引力， 文章的开篇段落要注明新闻对应北京时间多少和新闻报道来源出处，不需要小标题，段落适当位置用Bing image搜索添加相关图片，确保图片可以正常浏览！全文至少1000字'
        text_content = f'请充当文案高手。写作时，请保留原文的主题，并使用更加口水文和更强烈的情感表达，尤其是第一句。请勿打印文本以外的内容。中文输出时，字数限制在 150 字以内。忠实于原文的精髓，但要增强口语化的魅力和情感冲击力，尤其是一开始。没有多余的文字，只有纯粹的力量。最后一行内容和读者互动，保持在 150 字以内，末尾添加标签，使用中文，断行以增强感染力。内容是：[{ty.page_url}]'

        # # 6. 记录到Notion中
        page = {
            "title": ty.title,
            'chinese_title': title,
            'prompt': text_content,
            'id': ty.id,
            'area': 'tiyu',
            'datasource': datasource,
            'hmcturl': ty.page_url,
            'published_at': ty.published_at,
            'picture_url': image_url
        }
        try:
            newPage = notion.create_page(page=page)
            page_id = newPage['id']
            print(f'Save Notion: ID:{ty.id},pageId: {page_id},title: {title}')
            feed_util.save_fail_ids(ty.title, ty.id, 'success')
        except Exception as e:
            print(e)
            continue


def save_notion_dwfb():
    """同步短文发布-微头条发布"""
    while True:
        day = time_util.get_yesterday(4)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'area': 'tiyu',
        }
        contentDict = notion.get_content_by_condition_nba(params)
        if contentDict is None:
            break
        page_id = contentDict['page_id']
        page_chinese_title = contentDict['page_chinese_title']
        page_url = contentDict['page_url']
        image_url = contentDict['image_url']
        page_title = contentDict['page_title']
        print(f'待处理: page_chinese_title:{page_chinese_title},page_url:{page_url}')

        # 保存到另一个DB中
        content=llm_poe.call_content_common(query=page_title, origin_url=page_url, area='微体育')
        if content is None: continue
        
        # 6. 记录到Notion中
        page = {
            "title": page_title,
            'content': content,
            'area': '体育',
            'hmcturl': page_url,
            'picture_url': image_url
        }
        try:
            newPage = notion_dwfb.create_page_duanwen(page=page)
            new_page_id = newPage['id']
            notion.update_page_properties(page_id=page_id,tags='微头条发布成功', area='tiyu')
            print(f'Save Notion: pageId: {new_page_id},title: {page_title}')
        except Exception as e:
            print(e)
    # notion.update_page_content(page_id=page_id, properties_params="微头条发布成功" if flag else "微头条发布失败")




def main():
    '''
    1. 只用于数据采集和洗稿
    '''
    try:
        settings = llm_poe.get_settings()  # 获取剩余积分
        if settings is None: return None
        # 1. 保存所有Rss数据到Notion
        save_notion()
        # 1.2 同步存储微头条手动内容
        save_notion_dwfb()
    except Exception as e:
        print(f"体育异常:{e}")

"""全局配置"""
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)
notion_dwfb = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_DWFB)


if __name__ == '__main__':
    main()