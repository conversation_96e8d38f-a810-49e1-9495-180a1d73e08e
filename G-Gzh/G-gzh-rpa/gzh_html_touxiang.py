import os
import time
from dotenv import load_dotenv
import requests
from llm.llm_groq_tiyu import call_content_common
from util import notion_util
from util import gzh_parse_util
from util import  markdown_html_util
from util import downimg_util,gzh_api
load_dotenv()
import datetime
from wechatpy import WeChatClient


def yesterday(days:int):
    """多少天以前时间"""
    # 获取当前时间
    current_time = datetime.datetime.now()
    # 计算前一天的时间
    yesterday = current_time - datetime.timedelta(days=days)
    formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
    print(formatted_time)
    return formatted_time



class General:
    def __init__(self) -> None:
        print('初始化类')
        pass

    def _01_get_data(self,author):
        """从Notion获取数据-公众号文章URL"""
        try:
            # TODO： 获取指定用户的文章信息
            params = {
                # 'yesterday': yesterday(days=days),
                'readcount': readcount,
                # 'area':area,
                'author':author
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None: 
                print(f'没有找到符合条件的文章')
                return None,None
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')
            if '/' in page_title:
                page_title=page_title.replace('/','-')
            notion.update_page_properties(page_id=page_id,tags='格式化',area='头像壁纸')
            return page_url,page_id
        
        except Exception as e:
            print(f"头像异常:{e}")
    
    def _02_parse_data(self,ghzArticleUrl):
        try:
            return gzh_parse_util.get_weixin_content(ghzArticleUrl)
        except Exception as e:
            print(f'[异常]无法解析网址: {e}')
        return None,None,None


    def _03_llm_content(self,contentText):
        content_llm=self.summarize_with_groq(contentText)
        return content_llm

    def summarize_with_groq(self,content):
        """使用Groq总结文章中心思想和提取图片"""
        try:
            return call_content_common(content,area)
        except Exception as e:
            print(e)
        return None
    
    def _04_gzh_upload_image(self):
        return
    
    def _05_general_html(self, title: str, content: str, file_path: None, image_urls: list):
        # 校验并过滤图片URL
        valid_image_urls = []
        for img_url in image_urls:
            try:
                # Filter out URLs containing 'wx_fmt=gif' or 'wx_fmt=other'
                if 'wx_fmt=gif' in img_url or 'wx_fmt=other' in img_url or 'sz_mmbiz_png' in img_url:
                    print(f"排除不支持的图片格式: {img_url}")
                    continue
                # Ensure the URL starts with 'https://'
                if not img_url.startswith('https://'):
                    continue
                # 使用requests库检查URL是否可访问
                response = requests.head(img_url, timeout=5)
                if response.status_code == 200:
                    valid_image_urls.append(img_url)
                else:
                    print(f"排除无效的图片URL: {img_url}, 状态码: {response.status_code}")
            except requests.RequestException as e:
                print(f"无法访问图片URL: {img_url}, 错误: {str(e)}")
        
        # 更新image_urls为有效的URL列表   #路径gif去除, http去除 wx_fmt=gif other 去除.
        image_urls = valid_image_urls

        try:
            new_image_list=[]
            for idx, img_url in enumerate(image_urls):
                image_down_path=downimg_util.down_image_from_url(url=img_url,save_path=global_image_save_path)
                gzh_save_path=client.upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)
                # 首张图片-上传封面
                if idx == len(image_urls) - 1:
                    thumb_media_id=client.upload_cover_image(image_down_path)
            

            # 生成html文档
            file_path = f'{file_path}{title}.html'
            markdown_html_util.markdown_to_html(content,file_path,new_image_list)
            
            # 组装articles
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            article={
                'title':title,
                'author':global_author,
                'content':content,
                'thumb_media_id':thumb_media_id
            }
            return article
        except Exception as e:
            print(f'生成文档失败:{e}')
        return None

    def _06_gzh_create_draft(self,articles):
        # 3. 插入公众号草稿
        media_id=client.create_draft(articles)
        print(media_id)
        return media_id
    
    def _07_gzh_publish(slef,media_id):
        return client.publish_article(media_id)

        


def run():
    """
    1. 获取数据源
    2. 解析数据图片和内容
    3. AI生成内容
    4. 上传图片
    5. 整理图片和内容生成HTML
    6. 创建草稿
    """
    articles=[]
    pageIds=[]
    general=General()
    count = 0
    try:
        while True:
            docx_files = [f for f in os.listdir(global_destination_folder) if f.endswith('.html')]
            count += 1
            if count > 10: break  # 限定二十次
            if len(docx_files) < 2:  # 文件小于4篇持续
                # if len(articles)==0:
                #     # 先尝试获取 author=A 的数据-作为横向封面图
                #     # page_url, page_id = general._01_get_data(author='壁纸Pro')
                #     page_url, page_id = general._01_get_data(author='咸鱼猪图集')
                # else:
                #     # 如果没有 A 的数据,尝试获取 author=B 的数据
                #     page_url, page_id = general._01_get_data(author='咸鱼猪图集')

                page_url, page_id = general._01_get_data(author='咸鱼猪图集') #CuMamba  咸鱼猪图集
                if page_url is None: continue
                # page_url,page_id=general._01_get_data()
                weixin_title,text_content,pic_list=general._02_parse_data(page_url)
                if weixin_title is None: 
                    notion.update_page_properties(page_id=page_id,tags='内容失效',area='壁纸头像')
                    continue

                # 头像壁纸内容处理-图片列表
                new_image_list=[]
                for idx, img_url in enumerate(pic_list):
                    try:
                        if img_url.startswith('http://'): continue  # 作者头像图片不要
                        save_path=f'{global_image_save_path}{weixin_title}/'
                        # 如果文件夹不存在就创建
                        if not os.path.exists(save_path):
                            os.makedirs(save_path)
                        image_down_path = downimg_util.down_image_from_url(url=img_url, save_path=save_path)
                        gzh_img_url = client.upload_article_image(image_down_path)
                    
                        if gzh_img_url:
                            new_image_list.append(gzh_img_url)
                        
                        if idx == len(pic_list) - 2: #注意封面图片-剔除的数据要保证不异常
                            thumb_media_id = client.upload_cover_image(image_down_path)
                    except Exception as e:
                        print(f"处理图片时出错 {img_url}: {e}")
                        continue

                if not new_image_list:
                    raise ValueError("没有成功上传任何图片")

                file_path = f'{global_destination_folder}{weixin_title}.html'
                content = ""
                markdown_html_util.markdown_to_html_with_images(content, file_path, new_image_list)

                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                if thumb_media_id is None:
                    notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue

                article={
                    'title': weixin_title,
                    'author': global_author,
                    'content': content,
                    'thumb_media_id': thumb_media_id
                }


                if article is not None:
                    articles.append(article)
                    pageIds.append(page_id)
            else:
                print(f"{global_destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
                break
            time.sleep(1)  # Wait for 1 second before checking again

        media_id=general._06_gzh_create_draft(articles)
        print(f"[成功]创建草稿，media_id: {media_id}")
    except Exception as e:
        print(f"[失败]创建草稿: {e}")
        media_id = None
            
    # 草稿创建失败回滚
    if media_id is None:
        for page_id in pageIds:
            if page_id:
                try:
                    notion.update_page_properties(page_id=page_id,tags='初始化', area=area)
                except Exception as e:
                    print(f"更新Notion页面状态失败 (page_id: {page_id}): {e}")
    
        
    # general._07_gzh_publish(media_id)

    # 删除所有的HTML文件
    for f in os.listdir(global_destination_folder):
        if f.endswith('.html'):
            os.remove(os.path.join(global_destination_folder, f))
    print('[成功]:清理文件')



def main():
    try:  
        clientVer = WeChatClient(JIAOYU_JINJIN_WECHAT_APP_ID, JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET)
        token = clientVer.access_token
        print(f'[成功]JIAOYU_JINJIN_WECHAT_APP_ID: {token}')
    except Exception as e:
        print(f'[失败]token失效:{e}')
        return


    global client
    for i in range(1):
        if i==0:
            client=gzh_api.WECHATCLIENT(JIAOYU_JINJIN_WECHAT_APP_ID,JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET)
        # elif i==1:
        #     client=gzh_api.WECHATCLIENT(JIAOYU_JINJIN_WECHAT_APP_ID,JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET)

        for f in os.listdir(global_destination_folder):
            if f.endswith('.html'):
                os.remove(os.path.join(global_destination_folder, f))
        run()


"""全局配置"""
# author='咸鱼猪图集'
area='bizhitouxiang' 
readcount=1000
days=15

global_destination_folder = "/Volumes/文章存档/头像/待发布/"  # 目标文件夹
global_image_save_path=f'/Volumes/文章存档/Images/公众号/头像/' #体育图片存储文件夹
# global_destination_folder = "/Users/<USER>/Downloads/头像/待发布/"
# global_image_save_path=f'/Users/<USER>/Downloads/头像/Images/' #体育图片存储文件夹
global_author='金金'

NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)


JIAOYU_JINJIN_WECHAT_APP_ID = os.getenv('JIAOYU_JINJIN_WECHAT_APP_ID')
JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET = os.getenv('JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET')

"""第一层封面是横图，需要限定下，再设置后面的"""

if __name__ == "__main__":
    """改为美女壁纸头像-金金"""
    main()
    print('程序结束...')
   

