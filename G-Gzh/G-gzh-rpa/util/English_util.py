import re
def contains_english_word(paragraph):
    # 使用正则表达式匹配英文单词
    english_word_pattern = r'\b[a-zA-Z]+\b'
    # 如果找到了英文单词，返回True
    if re.search(english_word_pattern, paragraph):
        return True
    # 如果没有找到英文单词，返回False
    return False

test='''
### Broader Implications
The Celtics' triumph has significant implications for the NBA as a whole. It reiterates that perseverance and teamwork trump individual prowess in the quest for championship glory. Moreover, the Celtics' quest for Banner 18 has rekindled the rivalry with the Lakers, setting the stage for future battles between the two storied franchises. The media coverage of the Finals was nothing short of spectacular, with every game drawing record-breaking viewership numbers, showcasing the global appeal of the NBA.
'''
contains_english_word(test)