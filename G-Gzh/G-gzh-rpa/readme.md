# 逻辑: API发布公众号:
## 方案一:
1. 一次Notion获取一条数据
2. 数据创建公众号草稿,更新Notion数据状态
3. 根据草稿ID,每次从Notion获取数据,修改草稿,添加新数据index
4. 凑齐3条草稿图文消息,发布.

## 方案二: 
1. 新建草稿;
2. 修改草稿来实现添加新的图文消息;
3. 每次新建更新Notion数据状态,避免重复取数据;
4. 方案失败! 原因: 公众号没有草稿新增图文数据接口! 

## 方案三:
1. 一次性凑齐三篇文章的参数;
2. 将文章统一调用创建 草稿接口,形成最终草稿;


## 注意事项: 
1. 公众号发布接口API,没有推荐功能,没有认证的没有通知功能
2. 微信认证才能调用通知接口
3. 推荐功能,只能在页面上手动点击草稿才能发布; API调用不带有推荐,列表看不到.

## 方法说明：
1. 公众号内容数据采集同步Notion：gzh_spider.py
2. 本地内容生成：
3. 自动校验生成文章数量,不足继续触发调用生成,设定总共十次调用. 同时不足目录发布邮件通知;


## 公众号-体育领域:
	公众号-体育：
		1. 数据源： NBA新闻
		2. coze： link链接写作
			更新： 发布标识
		3. 文档生成： 到共享文件夹
		4. 美女文档： 
			1. 数据源：微信链接
			2. 程序： 自动生成
			3. 修改影刀： 校验文字去除，文档大小小于15M，才能导入文档。 
		5. 定时零点发布

## 公众号-育儿领域:
1. 按照领域筛选点赞量的内容.  领域+点赞+时间. 
	数据源: 次幂+公众号文章采集
2. 内容AI洗稿
3. 创建草稿: 每次创建3篇文章, 图片水印问题,
4. TODO:
	-  图片水印问题
	-  序号图片如何排除?
		- 图片全部都不要. 重新整理图片库.随机选择图片
		- 故事类内容,不能替换图片,需要用原文图片
		- 方案: #url路径gif去除, http去除 wx_fmt=gif other 去除.
	-  原标题洗稿:
		- 直接llm洗稿,保证原意,适当优化



## 安装:
pip install -U poe-api-wrapper


## 解决问题:
1. 影刀不能文章末尾添加模版
2. 跨系统标题标点符号乱码
3. 文案的样式排版格式, 不支持HTML. 可以尝试影刀全选HTML文件复制粘贴
