import os
import time
from re import findall

import requests
from bs4 import BeautifulSoup

from util import article_util
from util import system_util
from util import notion_util
from dotenv import load_dotenv

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")

weixin_title = ""
weixin_time = ""

area = '美女'
os_type = system_util.get_os()
destination_folder = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'


# 获取微信公众号内容,保存标题和时间
def get_weixin_html(url):
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')
    if temp is None:
        print(f'打不开:{url}')
        return None
    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


def general_meinv(weixin_url: str, area: str):
    # 1. 解析公众号正文
    content = get_weixin_html(weixin_url)
    if content is None: return False

    # 解析图片url
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)
    # 2. 本地存储
    os_type = system_util.get_os()
    # 6. 生成文档
    try:
        at = article_util.article()
        file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
        file_path = f'{file_path}【福利】{weixin_title}.docx'
        # flag=at.create_word_doc_content_meinv(file_path=file_path, contents=text_content, image_urls=pic_list) #带有正文
        flag = at.create_word_doc_content_meinv_onlyImage(file_path=file_path, image_urls=pic_list)  # 只有图片
        if flag: return True
    except Exception as e:
        print(f'生成美女文档异常{e}')
    return False


def run_sql():
    """公众号美女文档"""
    from util import mysql_table_article_util
    mysql_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': 'FuwenhaO594!',
        'db': 'wjh'
    }
    connector = mysql_table_article_util.MySQLConnector(mysql_config=mysql_config)
    connector.connect()
    # account_id = '838AqnyO'  # 已经用过:放大镜
    # account_id = 'PQDxgv4Q'  # 女帝聊体坛
    account_id = '4QkW5RyQ'  # CuMamba
    result = connector.select_data(table='wjh_articles', columns=['title', 'article_url'],
                                   condition=f'account_id="{account_id}"')

    for item in result:
        weixin_url = item['article_url']
        # '''1. 主要用于生成美女文档-体育账号'''
        general_meinv(weixin_url=weixin_url, area='美女')


def run_url():
    weixin_urls = [
        'https://mp.weixin.qq.com/s/oY89oHocOoP5DG7z1qDCnw',
    ]
    for weixin_url in weixin_urls:
        general_meinv(weixin_url=weixin_url, area='美女')


def run_notion():
    try:
        """通过Notion获取数据"""
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 8000,
            'author': '咸鱼猪图集',  # 球场放大镜 米酒说球 CuMamba 西西体育 女帝聊体坛 篮球教学论坛 篮球百科
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')
        flag = general_meinv(weixin_url=page_url, area='美女')
        notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")
    except Exception as e:
        print(f'美女异常:{e}')

def get_meinv_content(max_retries=3):
    """单独接口用于调用"""
    for attempt in range(max_retries):
        try:
            notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
            params = {
                'readcount': 5000,
                'author': '咸鱼猪图集',  # 球场放大镜 米酒说球 CuMamba 西西体育 女帝聊体坛 篮球教学论坛 篮球百科
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None:
                print(f"Attempt {attempt + 1}: Failed to get content from Notion. Retrying...")
                continue

            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

            content = get_weixin_html(page_url)
            if content is None:
                print(f"Attempt {attempt + 1}: Failed to get WeChat content. Retrying...")
                notion.update_page_content(page_id=page_id, properties_params="生成失败")
                continue

            # If we reach here, we have successfully retrieved the content
            pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
            pic_list = findall(pattern, content)
            title = f'【福利】{weixin_title}'
            notion.update_page_properties(page_id=page_id,tags='格式化',area='体育')
            return title, pic_list,page_id

        except Exception as e:
            print(f'Attempt {attempt + 1}: 美女异常:{e}')

    # If we've exhausted all retries
    print(f"Failed to get content after {max_retries} attempts.")
    return None, None

def main():
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 10: break  # 限定二十次
        if len(docx_files) < 15:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    # run_sql()
    # run_url()
    # run_notion()
    # main()
    get_meinv_content()
