# teststr="""
# ## 3天闪婚45万打水漂！90后小伙血亏后痛诉：我到底娶了个啥？！

# "第一次见面吃了顿饭，第二天就谈好彩礼，第三天就领证了。"90后小伙老王苦笑着回忆这段堪称"极速"的婚姻。2024年初，在父母的催促下，他通过某婚介所认识了来自贵州的小芳。**短短72小时内，这场闪婚耗费了他45万元，包括30万彩礼和15万中介费**。然而婚后不到半年，小芳便人间蒸发，只在桌上留下一张写着"对不起"的字条。

# 更让老王崩溃的是，他发现自己并非第一个"受害者"。同一家婚介所还有十几位男青年遭遇类似经历。**据统计，这些闪婚案例中，从相亲到领证平均用时仅2.8天，男方支出普遍在20-45万之间**。婚介所打出"7天内包您脱单"的广告，利用着年轻人的择偶焦虑和家庭压力。

# 婚检、婚前调查？统统没有。"当时觉得找个媳妇不容易，现在想想简直是上了贼船。"老王的父亲含泪控诉道。他们已经向警方报案，但追回损失的可能性极低。

# "笑死，这不就是收智商税吗？"网友小美直呼这简直是明目张胆的"彩礼诈骗"。还有人调侃："这结婚速度都快赶上买菜了，能指望有好结果？"

# 也有人表示理解："现在大龄青年压力太大了，农村男青年更是难，被逼到这份上也是无奈之举。"更多网友则呼吁加强对婚介行业的监管："这哪是婚介所，简直就是专业化的骗婚团伙！"

# 近年来，类似的闪婚骗局层出不穷。2023年，浙江曾破获一起特大婚托诈骗案，涉案金额高达3000万元。另一起发生在山东的案件中，一个由30人组成的骗婚团伙，专门物色经济条件优越的单身男性，半年内就骗取了500多万元。

# 与老王遭遇相似的是，这些案件往往都具有三个共同特征：一是婚介所充当中间人，二是女方来自经济欠发达地区，三是整个婚恋过程快得不合常理。据民政部门统计，2023年全国因闪婚导致的离婚案件同比增长35%，平均婚姻持续时间不到8个月。

# 在这个万物皆可速成的时代，婚姻却是最不该急功近利的。当"闪婚速成班"横行市场，当感情变成了纯粹的金钱交易，我们是不是应该停下来想想：为什么那么多人宁愿冒着巨大风险，也要选择这种极端的方式？

# 也许，解决之道不在于谴责被骗者的轻信，而是要从根源上消除适龄青年的婚恋焦虑，建立更健康的婚恋观。毕竟，好的婚姻需要用心经营，而不是用钱买来。各位读者，你们觉得闪婚是无奈之选，还是愚蠢行为？欢迎在评论区分享你的观点。"""

# def extract_title_content(markdown_text: str) -> tuple[str, str]:
#     """
#     Extract title and content from markdown text.
#     Returns tuple of (title, content)
#     """
#     # Clean input text
#     text = markdown_text.strip()
    
#     # Extract title - look for markdown h1/h2 header
#     lines = text.split('\n')
#     title = ''
#     content = ''
    
#     # Find title from first h1/h2 header
#     for line in lines:
#         if line.strip().startswith(('#', '##')):
#             title = line.strip('# ')
#             # Get content starting from next line after title
#             content_lines = lines[lines.index(line) + 1:]
#             content = '\n'.join(content_lines).strip()
#             break
            
#     return title, content

# # Example usage:
# title, content = extract_title_content(teststr)
# print("Title:", title)
# print("\nContent:", content)

for i in range(2):
    print(i)
