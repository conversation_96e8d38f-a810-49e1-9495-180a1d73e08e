import os

from llm.llm_groq_tiyu import call_content_common
from util import gzh_parse_util
from util import  markdown_html_util
from util import downimg_util,gzh_api
from util import jina_util
from util import ddg_search_util
from llm import llm_poe
from dotenv import load_dotenv
load_dotenv()



class General:
    def __init__(self) -> None:
        print('General init...')
        pass

    def _01_get_data(self):
        """从Notion获取数据-公众号文章URL"""
        try:
            params = {
                'readcount': readcount,
                'area':area
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None: return None,None
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')
            
            notion.update_page_properties(page_id=page_id,tags='格式化',area='教育')
            return page_url,page_id
        
        except Exception as e:
            print(f"育儿异常:{e}")
    
    def _02_parse_data(self,ghzArticleUrl):
        try:
            return gzh_parse_util.get_weixin_content(ghzArticleUrl)
        except Exception as e:
            print(f'[异常]无法解析网址: {e}')
        return None,None,None


    def _03_llm_content(self,contentText):
        content_llm=self.summarize_with_groq(contentText)
        return content_llm

    def summarize_with_groq(self,content):
        """使用Groq总结文章中心思想和提取图片"""
        try:
            return call_content_common(content,area)
        except Exception as e:
            print(e)
        return None
    
    def _04_gzh_upload_image(self):
        return
    
    def _05_general_html(self, title: str, content: str, file_path: None, image_urls: list):
        try:
            new_image_list=[]
            for idx, img_url in enumerate(image_urls):
                image_down_path=downimg_util.down_image_from_url(url=img_url,save_path=global_image_save_path)
                gzh_save_path=client.upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)
                # 首张图片-上传封面
                if idx == len(image_urls) - 1:
                    thumb_media_id=client.upload_cover_image(image_down_path)
            
            if thumb_media_id is None:
                print("thumb_media_id is None")
                return None
            
            # 生成html文档
            file_path = f'{file_path}{title}.html'
            markdown_html_util.markdown_to_html(content,file_path,new_image_list)
            
            # 修改读取文件的部分
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            # 处理序号和内容对齐问题
            # content = self._fix_content_formatting(content)
            
            article = {
                'title': title,
                'author': global_author,
                'content': content,
                'thumb_media_id': thumb_media_id
            }
            return article
        except Exception as e:
            print(f'生成文档失败:{e}')
        return None

    def _fix_content_formatting(self, content):
        """修复内容格式化问题，只处理序号部分"""
        import re
        from bs4 import BeautifulSoup

        # 1. 解析HTML
        soup = BeautifulSoup(content, 'html.parser')
        
        # 2. 找到所有有序列表
        ol_elements = soup.find_all('ol')
        
        for ol in ol_elements:
            # 获取所有列表项
            list_items = ol.find_all('li')
            
            # 重新格式化每个列表项
            for idx, li in enumerate(list_items, 1):
                # 获取段落内容
                p = li.find('p')
                if p:
                    # 移除多余的空白字符
                    text = ' '.join(p.get_text().split())
                    # 重新设置段落内容，保持原有的HTML属性
                    p.clear()
                    p.append(f"{idx}. {text}")
        
        return str(soup)

    def _06_gzh_create_draft(self,articles):
        # 3. 插入公众号草稿
        media_id=client.create_draft(articles)
        print(media_id)
        return media_id
    
    def _07_gzh_publish(slef,media_id):
        return client.publish_article(media_id)

   

def run():
    """清风侃侃账号清洗
    1. 删除所有发布的内容
    2. 优化文章生成-专注热点时事新闻
    3. 图文生成
    4. 生成草稿
    5. 校验文章排版格式

    执行说明:
    1. env环境配置API-key
    2. 
    """
    # 删除所有的HTML文件
    for f in os.listdir(global_destination_folder):
        if f.endswith('.html'):
            os.remove(os.path.join(global_destination_folder, f))
    print('[成功]:清理文件')

    
    general=General()
    # 1. 删除发布内容- 每日限制调用十次- 内容倒序删除
    client.delete_all_published()

    articles=[]
    
    for query in querys:
        # 2. 检索图片- 从文章中获取- 获取文章
        results = ddg_search_util.DDGSTextSearch.run(query)
        article_href=''
        # 只要三张图片
        collected_images = []
        for result in results:
            print(result)
            article_href = result.get('href', '')
            if 'sohu.com' in article_href:continue #搜狐不能解析
            # 3.1 解析文章
            images = jina_util.extract_images_from_url(article_href)
            if images:
                # 只添加需要的图片数量
                collected_images.extend(images[:MAX_IMAGES - len(collected_images)])
                for img_url in collected_images:
                    print(f"- {img_url}")
                # 如果已收集够3张图片就终止
                if len(collected_images) >= MAX_IMAGES:
                    break
            else:
                print(f'[失败]无法解析出图片:{article_href}')    
        if len(collected_images)==0:
            print(f'[失败]没有图片:{query}')
            continue


        # 3. 生成文章-POE-数据源(手动输入-Notion筛选)
        content_llm=llm_poe.call_content_common(query,None,'热点')
        if content_llm is None or len(content_llm)<500: 
            print(f'[失败]没有AI文章:{query}')
        ## 解析标题和内容
        # Example usage:
        title, content = extract_title_content(content_llm)
       
        # 4. 生成HTML
        article=general._05_general_html(title,content,global_destination_folder,collected_images)
        articles.append(article)
    
    # 5. 发布草稿
    if len(articles)==0:return
    media_id=general._06_gzh_create_draft(articles)
    if media_id is None:
        print('[失败]创建草稿失败')
    else:
        print(f"[成功]创建草稿，media_id: {media_id}")

    # 6. 人工核对
def extract_title_content(markdown_text: str) -> tuple[str, str]:
    """
    Extract title and content from markdown text.
    Returns tuple of (title, content)
    """
    # Clean input text
    text = markdown_text.strip()
    
    # Extract title - look for markdown h1/h2 header
    lines = text.split('\n')
    title = ''
    content = ''
    
    # Find title from first h1/h2 header
    for line in lines:
        if line.strip().startswith(('#', '##')):
            title = line.strip('# ')
            # Get content starting from next line after title
            content_lines = lines[lines.index(line) + 1:]
            content = '\n'.join(content_lines).strip()
            break
            
    return title, content

"""全局配置"""
JIAOYU_QINGFENG_WECHAT_APP_ID = os.getenv('JIAOYU_QINGFENG_WECHAT_APP_ID')
JIAOYU_QINGFENG_WECHAT_APP_SECRET = os.getenv('JIAOYU_QINGFENG_WECHAT_APP_SECRET')
client=gzh_api.WECHATCLIENT(JIAOYU_QINGFENG_WECHAT_APP_ID,JIAOYU_QINGFENG_WECHAT_APP_SECRET)
global_destination_folder = "/tmp/公众号/文章存档/热点/待发布/"  # 目标文件夹
global_image_save_path=f'/tmp/公众号/文章存档/Images/公众号/热点/' #体育图片存储文件夹
global_author='清风'

# 确保文件夹存在
os.makedirs(global_destination_folder, exist_ok=True)
os.makedirs(global_image_save_path, exist_ok=True)

MAX_IMAGES = 2 #图片数量
    
querys=[
    '对话王星、嘉嘉：逃离妙瓦底'
]

if __name__ == '__main__':
    print("开始执行...")
    run()