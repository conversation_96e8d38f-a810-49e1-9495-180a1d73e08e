# -*- coding: utf-8 -*-
"""
高级SimilarWeb数据采集器
包含重试机制、错误处理、日志记录等高级功能
"""

import requests
import json
import time
import csv
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs
import re
from config import REQUEST_CONFIG, SAVE_CONFIG, API_ENDPOINTS


class AdvancedSimilarWebSpider:
    def __init__(self, cookies: str = None, log_level: str = "INFO"):
        """
        初始化高级SimilarWeb爬虫
        
        Args:
            cookies: SimilarWeb的Cookie字符串
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        """
        self.session = requests.Session()
        self.base_url = "https://pro.similarweb.com"
        
        # 设置日志
        self.setup_logging(log_level)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://pro.similarweb.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        
        if cookies:
            self.set_cookies(cookies)
        
        self.logger.info("AdvancedSimilarWebSpider 初始化完成")
    
    def setup_logging(self, log_level: str):
        """设置日志配置"""
        # 创建logs目录
        os.makedirs('logs', exist_ok=True)
        
        # 配置日志
        log_filename = f"logs/similarweb_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def set_cookies(self, cookies: str):
        """设置Cookie"""
        cookie_dict = {}
        for cookie in cookies.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookie_dict[name] = value
        
        self.session.cookies.update(cookie_dict)
        self.logger.info(f"已设置 {len(cookie_dict)} 个Cookie")
    
    def make_request_with_retry(self, url: str, params: Dict = None) -> Optional[Dict]:
        """
        带重试机制的请求方法
        
        Args:
            url: 请求URL
            params: 请求参数
            
        Returns:
            响应数据或None
        """
        retry_times = REQUEST_CONFIG.get('retry_times', 3)
        retry_delay = REQUEST_CONFIG.get('retry_delay', 2)
        timeout = REQUEST_CONFIG.get('timeout', 30)
        
        for attempt in range(retry_times):
            try:
                self.logger.debug(f"尝试请求 {url} (第{attempt+1}次)")
                
                response = self.session.get(
                    url, 
                    headers=self.headers, 
                    params=params, 
                    timeout=timeout
                )
                response.raise_for_status()
                
                data = response.json()
                self.logger.info(f"请求成功: {url}")
                return data
                
            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时 (第{attempt+1}次): {url}")
            except requests.exceptions.HTTPError as e:
                self.logger.error(f"HTTP错误 (第{attempt+1}次): {e}")
                if response.status_code == 401:
                    self.logger.error("认证失败，请检查Cookie是否有效")
                    break
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (第{attempt+1}次): {e}")
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败 (第{attempt+1}次): {e}")
            
            if attempt < retry_times - 1:
                self.logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
        
        self.logger.error(f"请求最终失败: {url}")
        return None
    
    def get_api_data(self, endpoint: str, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        通用API数据获取方法
        
        Args:
            endpoint: API端点名称
            domain: 目标域名
            country: 国家代码
            duration: 时间范围
            
        Returns:
            API响应数据
        """
        if endpoint not in API_ENDPOINTS:
            self.logger.error(f"不支持的API端点: {endpoint}")
            return {}
        
        api_url = f"{self.base_url}{API_ENDPOINTS[endpoint]}"
        
        params = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'webSource': 'Total'
        }
        
        self.logger.info(f"获取 {domain} 的 {endpoint} 数据")
        data = self.make_request_with_retry(api_url, params)
        
        if data:
            self.logger.info(f"成功获取 {domain} 的 {endpoint} 数据")
        else:
            self.logger.error(f"获取 {domain} 的 {endpoint} 数据失败")
        
        return data or {}
    
    def collect_comprehensive_data(self, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        收集网站的综合数据
        
        Args:
            domain: 目标域名
            country: 国家代码
            duration: 时间范围
            
        Returns:
            包含所有数据的字典
        """
        self.logger.info(f"开始收集 {domain} 的综合数据...")
        
        all_data = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'collected_at': datetime.now().isoformat(),
            'success_count': 0,
            'total_apis': 0,
            'data': {}
        }
        
        # 定义要采集的API端点
        endpoints_to_collect = ['overview', 'traffic', 'audience']
        all_data['total_apis'] = len(endpoints_to_collect)
        
        for endpoint in endpoints_to_collect:
            try:
                data = self.get_api_data(endpoint, domain, country, duration)
                if data:
                    all_data['data'][endpoint] = data
                    all_data['success_count'] += 1
                    self.logger.info(f"✅ {endpoint} 数据采集成功")
                else:
                    self.logger.warning(f"❌ {endpoint} 数据采集失败")
                
                # 请求间隔
                request_delay = REQUEST_CONFIG.get('request_delay', 1)
                time.sleep(request_delay)
                
            except Exception as e:
                self.logger.error(f"采集 {endpoint} 数据时出错: {e}")
        
        success_rate = (all_data['success_count'] / all_data['total_apis']) * 100
        self.logger.info(f"数据收集完成: {domain}, 成功率: {success_rate:.1f}%")
        
        return all_data
    
    def save_data_with_backup(self, data: Dict, base_filename: str = None):
        """
        保存数据并创建备份
        
        Args:
            data: 要保存的数据
            base_filename: 基础文件名
        """
        if not base_filename:
            domain = data.get('domain', 'unknown')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_filename = f"similarweb_{domain}_{timestamp}"
        
        # 确保数据目录存在
        data_dir = SAVE_CONFIG.get('data_dir', 'data')
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(f"{data_dir}/backup", exist_ok=True)
        
        try:
            # 保存JSON格式
            if SAVE_CONFIG.get('save_json', True):
                json_file = os.path.join(data_dir, f"{base_filename}.json")
                backup_json = os.path.join(data_dir, "backup", f"{base_filename}_backup.json")
                
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # 创建备份
                with open(backup_json, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                self.logger.info(f"JSON数据已保存: {json_file}")
            
            # 保存CSV格式
            if SAVE_CONFIG.get('save_csv', True):
                csv_file = os.path.join(data_dir, f"{base_filename}.csv")
                self.save_to_csv_advanced(data, csv_file)
                self.logger.info(f"CSV数据已保存: {csv_file}")
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
    
    def save_to_csv_advanced(self, data: Dict, filepath: str):
        """
        高级CSV保存方法
        
        Args:
            data: 要保存的数据
            filepath: 文件路径
        """
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入元数据
                writer.writerow(['=== 采集信息 ==='])
                writer.writerow(['域名', data.get('domain', '')])
                writer.writerow(['国家', data.get('country', '')])
                writer.writerow(['时间范围', data.get('duration', '')])
                writer.writerow(['采集时间', data.get('collected_at', '')])
                writer.writerow(['成功API数', f"{data.get('success_count', 0)}/{data.get('total_apis', 0)}"])
                writer.writerow([])
                
                # 写入各API数据
                for api_name, api_data in data.get('data', {}).items():
                    writer.writerow([f'=== {api_name.upper()} 数据 ==='])
                    self._write_nested_data(writer, api_data)
                    writer.writerow([])
                    
        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
    
    def _write_nested_data(self, writer, data: Dict, prefix: str = '', level: int = 0):
        """递归写入嵌套数据"""
        indent = "  " * level
        
        for key, value in data.items():
            if isinstance(value, dict):
                writer.writerow([f"{indent}{prefix}{key}", ""])
                self._write_nested_data(writer, value, f"{prefix}{key}_", level + 1)
            elif isinstance(value, list):
                writer.writerow([f"{indent}{prefix}{key}", f"列表({len(value)}项)"])
                for i, item in enumerate(value[:10]):  # 限制显示前10项
                    if isinstance(item, dict):
                        writer.writerow([f"{indent}  [{i+1}]", ""])
                        self._write_nested_data(writer, item, f"{prefix}{key}_{i+1}_", level + 2)
                    else:
                        writer.writerow([f"{indent}  [{i+1}]", str(item)])
            else:
                writer.writerow([f"{indent}{prefix}{key}", str(value)])


def main():
    """主函数"""
    print("高级SimilarWeb数据采集器")
    print("=" * 50)
    
    # 这里需要配置实际的Cookie
    cookies = """
    请在这里粘贴您的SimilarWeb Cookie信息
    """
    
    if "请在这里粘贴" in cookies:
        print("⚠️  请先设置有效的Cookie信息!")
        return
    
    # 创建高级爬虫实例
    spider = AdvancedSimilarWebSpider(cookies=cookies, log_level="INFO")
    
    # 采集数据
    domain = "klingai.com"
    data = spider.collect_comprehensive_data(domain, country="999", duration="1m")
    
    if data and data.get('success_count', 0) > 0:
        spider.save_data_with_backup(data)
        print("✅ 数据采集完成!")
    else:
        print("❌ 数据采集失败")


if __name__ == "__main__":
    main()
