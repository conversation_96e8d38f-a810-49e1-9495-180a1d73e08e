# -*- coding: utf-8 -*-
"""
SimilarWeb连接调试工具
用于诊断API请求失败的原因
"""

from get_similarweb import SimilarWebSpider
import requests
import json


def debug_connection():
    """
    调试连接问题
    """
    print("🔧 SimilarWeb连接调试工具")
    print("=" * 50)
    
    # 获取Cookie
    print("📋 步骤1: 输入Cookie信息")
    cookies = input("请粘贴您的SimilarWeb Cookie: ").strip()
    
    if not cookies:
        print("❌ 未提供Cookie，无法进行调试")
        return
    
    # 创建爬虫实例
    print("\n🔧 步骤2: 创建爬虫实例")
    spider = SimilarWebSpider(cookies=cookies)
    
    # 测试连接
    print("\n🧪 步骤3: 测试连接")
    success = spider.test_connection("klingai.com")
    
    if success:
        print("\n✅ 连接测试成功！可以开始数据采集")
        
        # 尝试获取实际数据
        print("\n📊 步骤4: 尝试获取实际数据")
        data = spider.get_website_overview("klingai.com")
        
        if data:
            print("✅ 数据获取成功！")
            print(f"📊 数据预览: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
        else:
            print("❌ 数据获取失败")
    else:
        print("\n❌ 连接测试失败")
        print("\n🔍 可能的解决方案:")
        print("1. 检查Cookie是否完整且未过期")
        print("2. 确认SimilarWeb账户有效且有权限")
        print("3. 检查网络连接")
        print("4. 尝试重新登录SimilarWeb获取新Cookie")


def manual_api_test():
    """
    手动API测试
    """
    print("\n🔬 手动API测试")
    print("-" * 30)
    
    cookies = input("请粘贴Cookie: ").strip()
    if not cookies:
        print("❌ 未提供Cookie")
        return
    
    # 解析Cookie
    cookie_dict = {}
    for cookie in cookies.split(';'):
        if '=' in cookie:
            name, value = cookie.strip().split('=', 1)
            cookie_dict[name] = value
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://pro.similarweb.com/',
    }
    
    # 测试不同的API端点
    api_tests = [
        {
            'name': '网站概览API',
            'url': 'https://pro.similarweb.com/api/WebsiteOverview/getheader',
            'params': {
                'keys': 'klingai.com',
                'mainDomainOnly': 'false',
                'includeCrossData': 'true',
                'webSource': 'Total',
                'country': '999'
            }
        },
        {
            'name': '流量数据API',
            'url': 'https://pro.similarweb.com/api/websiteanalysis/traffic-overview',
            'params': {
                'keys': 'klingai.com',
                'country': '999',
                'duration': '1m',
                'webSource': 'Total'
            }
        }
    ]
    
    session = requests.Session()
    session.cookies.update(cookie_dict)
    
    for test in api_tests:
        print(f"\n🧪 测试: {test['name']}")
        print(f"📡 URL: {test['url']}")
        print(f"📋 参数: {test['params']}")
        
        try:
            response = session.get(
                test['url'], 
                headers=headers, 
                params=test['params'], 
                timeout=30
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📏 响应长度: {len(response.content)} bytes")
            print(f"🔧 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON解析成功")
                    print(f"📊 数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"📊 数据键: {list(data.keys())}")
                    print(f"📄 数据预览: {json.dumps(data, indent=2, ensure_ascii=False)[:300]}...")
                except json.JSONDecodeError:
                    print(f"❌ JSON解析失败")
                    print(f"📄 响应内容: {response.text[:300]}...")
            else:
                print(f"❌ 请求失败")
                print(f"📄 错误内容: {response.text[:300]}...")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")


def check_cookie_format():
    """
    检查Cookie格式
    """
    print("\n🍪 Cookie格式检查")
    print("-" * 30)
    
    cookies = input("请粘贴Cookie: ").strip()
    
    if not cookies:
        print("❌ 未提供Cookie")
        return
    
    print(f"📏 Cookie长度: {len(cookies)} 字符")
    
    # 解析Cookie
    cookie_parts = cookies.split(';')
    print(f"🔢 Cookie项目数: {len(cookie_parts)}")
    
    valid_cookies = 0
    for i, cookie in enumerate(cookie_parts, 1):
        cookie = cookie.strip()
        if '=' in cookie:
            name, value = cookie.split('=', 1)
            print(f"  {i}. {name}: {value[:20]}..." if len(value) > 20 else f"  {i}. {name}: {value}")
            valid_cookies += 1
        else:
            print(f"  {i}. ⚠️  无效格式: {cookie}")
    
    print(f"✅ 有效Cookie数: {valid_cookies}/{len(cookie_parts)}")
    
    # 检查关键Cookie
    cookie_dict = {}
    for cookie in cookie_parts:
        if '=' in cookie:
            name, value = cookie.strip().split('=', 1)
            cookie_dict[name] = value
    
    important_cookies = ['session', 'auth', 'token', 'user', 'login']
    found_important = []
    
    for key in cookie_dict.keys():
        for important in important_cookies:
            if important.lower() in key.lower():
                found_important.append(key)
    
    if found_important:
        print(f"🔑 发现重要Cookie: {found_important}")
    else:
        print("⚠️  未发现明显的认证相关Cookie")


def main():
    """
    主菜单
    """
    while True:
        print("\n🔧 SimilarWeb调试工具菜单")
        print("=" * 40)
        print("1. 完整连接调试")
        print("2. 手动API测试")
        print("3. Cookie格式检查")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            debug_connection()
        elif choice == '2':
            manual_api_test()
        elif choice == '3':
            check_cookie_format()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()
