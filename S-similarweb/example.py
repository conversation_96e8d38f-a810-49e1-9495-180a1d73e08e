# -*- coding: utf-8 -*-
"""
SimilarWeb爬虫使用示例
演示如何使用SimilarWebSpider进行数据采集
"""

from get_similarweb import SimilarWebSpider
from config import SIMILARWEB_COOKIES, DEFAULT_PARAMS
import json


def example_single_domain():
    """
    示例1: 采集单个域名的数据
    """
    print("=== 示例1: 采集单个域名数据 ===")
    
    # 检查Cookie配置
    if "请在这里粘贴" in SIMILARWEB_COOKIES:
        print("❌ 请先在config.py中配置有效的Cookie信息")
        return
    
    # 创建爬虫实例
    spider = SimilarWebSpider(cookies=SIMILARWEB_COOKIES)
    
    # 目标域名
    domain = "klingai.com"
    
    print(f"正在采集 {domain} 的数据...")
    
    # 采集数据
    data = spider.collect_all_data(
        domain=domain,
        country="999",  # 全球数据
        duration="1m"   # 1个月
    )
    
    if data:
        # 保存数据
        spider.save_to_json(data, f"example_{domain}_data.json")
        spider.save_to_csv(data, f"example_{domain}_data.csv")
        print(f"✅ {domain} 数据采集完成")
    else:
        print(f"❌ {domain} 数据采集失败")


def example_multiple_domains():
    """
    示例2: 批量采集多个域名的数据
    """
    print("\n=== 示例2: 批量采集多个域名数据 ===")
    
    # 检查Cookie配置
    if "请在这里粘贴" in SIMILARWEB_COOKIES:
        print("❌ 请先在config.py中配置有效的Cookie信息")
        return
    
    # 创建爬虫实例
    spider = SimilarWebSpider(cookies=SIMILARWEB_COOKIES)
    
    # 目标域名列表
    domains = [
        "klingai.com",
        "openai.com", 
        "anthropic.com",
        "google.com"
    ]
    
    all_results = []
    
    for domain in domains:
        print(f"正在采集 {domain} 的数据...")
        
        try:
            data = spider.collect_all_data(
                domain=domain,
                country="999",
                duration="1m"
            )
            
            if data:
                all_results.append(data)
                print(f"✅ {domain} 数据采集成功")
            else:
                print(f"❌ {domain} 数据采集失败")
                
        except Exception as e:
            print(f"❌ {domain} 采集出错: {e}")
        
        # 避免请求过快
        import time
        time.sleep(2)
    
    # 保存批量结果
    if all_results:
        batch_data = {
            "collected_at": spider.collect_all_data("example.com")["collected_at"] if all_results else "",
            "total_domains": len(all_results),
            "results": all_results
        }
        
        spider.save_to_json(batch_data, "batch_results.json")
        print(f"✅ 批量采集完成，共采集 {len(all_results)} 个域名")


def example_different_params():
    """
    示例3: 使用不同参数采集数据
    """
    print("\n=== 示例3: 不同参数采集数据 ===")
    
    # 检查Cookie配置
    if "请在这里粘贴" in SIMILARWEB_COOKIES:
        print("❌ 请先在config.py中配置有效的Cookie信息")
        return
    
    spider = SimilarWebSpider(cookies=SIMILARWEB_COOKIES)
    domain = "klingai.com"
    
    # 不同的参数组合
    param_combinations = [
        {"country": "999", "duration": "1m", "desc": "全球-1个月"},
        {"country": "840", "duration": "3m", "desc": "美国-3个月"},
        {"country": "156", "duration": "6m", "desc": "中国-6个月"},
    ]
    
    for params in param_combinations:
        print(f"采集参数: {params['desc']}")
        
        try:
            data = spider.collect_all_data(
                domain=domain,
                country=params["country"],
                duration=params["duration"]
            )
            
            if data:
                filename = f"{domain}_{params['country']}_{params['duration']}"
                spider.save_to_json(data, f"{filename}.json")
                print(f"✅ {params['desc']} 数据采集成功")
            else:
                print(f"❌ {params['desc']} 数据采集失败")
                
        except Exception as e:
            print(f"❌ {params['desc']} 采集出错: {e}")
        
        import time
        time.sleep(2)


def example_parse_url():
    """
    示例4: 从SimilarWeb URL解析参数
    """
    print("\n=== 示例4: URL参数解析 ===")
    
    spider = SimilarWebSpider()
    
    # 示例URL
    test_urls = [
        "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com",
        "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/840/3m?webSource=Total&key=openai.com",
        "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/156/6m?webSource=Total&key=google.com"
    ]
    
    for url in test_urls:
        print(f"\nURL: {url}")
        params = spider.parse_url_params(url)
        print(f"解析结果: {json.dumps(params, indent=2, ensure_ascii=False)}")


def main():
    """
    主函数 - 运行所有示例
    """
    print("SimilarWeb爬虫使用示例")
    print("=" * 60)
    
    # 运行示例
    example_parse_url()  # 这个不需要Cookie，先运行
    
    # 以下示例需要有效的Cookie
    example_single_domain()
    example_multiple_domains() 
    example_different_params()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成!")
    print("请查看生成的数据文件:")
    print("- JSON文件: 包含完整的结构化数据")
    print("- CSV文件: 便于Excel等工具分析的表格数据")


if __name__ == "__main__":
    main()
