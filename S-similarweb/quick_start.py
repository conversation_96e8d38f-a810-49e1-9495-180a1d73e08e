# -*- coding: utf-8 -*-
"""
SimilarWeb数据采集器 - 快速开始指南
"""

from get_similarweb import SimilarWebSpider


def quick_start():
    """
    快速开始 - 只需要配置Cookie即可使用
    """
    print("🚀 SimilarWeb数据采集器 - 快速开始")
    print("=" * 50)
    
    # 步骤1: 配置Cookie
    print("📋 步骤1: 配置Cookie")
    print("请按以下步骤获取Cookie:")
    print("1. 登录 https://pro.similarweb.com")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 在Network标签页中找到任意请求")
    print("4. 复制Cookie信息")
    print()
    
    # 这里需要用户输入实际的Cookie
    cookies = input("请粘贴您的Cookie信息 (或按Enter跳过演示): ").strip()
    
    if not cookies:
        print("⚠️  跳过Cookie配置，仅演示URL解析功能")
        demo_url_parsing()
        return
    
    # 步骤2: 创建爬虫实例
    print("\n📡 步骤2: 创建爬虫实例")
    spider = SimilarWebSpider(cookies=cookies)
    print("✅ 爬虫实例创建成功")
    
    # 步骤3: 输入目标URL或域名
    print("\n🎯 步骤3: 输入目标")
    target = input("请输入目标域名或完整URL (默认: klingai.com): ").strip()
    
    if not target:
        target = "klingai.com"
    
    # 判断是URL还是域名
    if target.startswith('http'):
        print(f"解析URL: {target}")
        params = spider.parse_url_params(target)
        domain = params.get('domain')
        country = params.get('country', '999')
        duration = params.get('duration', '1m')
        print(f"提取的参数: 域名={domain}, 国家={country}, 时间范围={duration}")
    else:
        domain = target
        country = '999'
        duration = '1m'
        print(f"使用域名: {domain}")
    
    # 步骤4: 采集数据
    print(f"\n📊 步骤4: 采集 {domain} 的数据")
    print("正在采集数据，请稍候...")
    
    try:
        data = spider.collect_all_data(domain, country, duration)
        
        if data and any([data.get('overview'), data.get('traffic'), data.get('audience')]):
            print("✅ 数据采集成功!")
            
            # 步骤5: 保存数据
            print("\n💾 步骤5: 保存数据")
            spider.save_to_json(data)
            spider.save_to_csv(data)
            print("✅ 数据已保存到 data/ 目录")
            
            # 显示数据摘要
            print("\n📈 数据摘要:")
            print(f"域名: {data.get('domain')}")
            print(f"采集时间: {data.get('collected_at')}")
            print(f"概览数据: {'✅' if data.get('overview') else '❌'}")
            print(f"流量数据: {'✅' if data.get('traffic') else '❌'}")
            print(f"受众数据: {'✅' if data.get('audience') else '❌'}")
            
        else:
            print("❌ 数据采集失败")
            print("可能的原因:")
            print("- Cookie已过期或无效")
            print("- 网络连接问题")
            print("- 域名不存在或无权限访问")
            
    except Exception as e:
        print(f"❌ 采集过程中出错: {e}")
    
    print("\n🎉 快速开始完成!")


def demo_url_parsing():
    """
    演示URL解析功能 (不需要Cookie)
    """
    print("\n🔍 URL解析功能演示")
    print("-" * 30)
    
    spider = SimilarWebSpider()
    
    # 测试URL
    test_urls = [
        "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com",
        "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/840/3m?webSource=Total&key=openai.com",
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n示例 {i}:")
        print(f"URL: {url}")
        params = spider.parse_url_params(url)
        print(f"解析结果:")
        print(f"  域名: {params.get('domain')}")
        print(f"  国家: {params.get('country')}")
        print(f"  时间范围: {params.get('duration')}")
        print(f"  数据源: {params.get('webSource')}")


def show_help():
    """
    显示帮助信息
    """
    print("📚 SimilarWeb数据采集器 - 帮助信息")
    print("=" * 50)
    print()
    print("🔧 主要功能:")
    print("- 网站概览数据采集")
    print("- 流量统计数据获取")
    print("- 受众分析数据收集")
    print("- 多格式数据导出 (JSON/CSV)")
    print()
    print("📁 文件说明:")
    print("- get_similarweb.py    : 主程序")
    print("- advanced_spider.py   : 高级版本(含日志和重试)")
    print("- example.py          : 使用示例")
    print("- config.py           : 配置文件")
    print("- quick_start.py      : 快速开始指南")
    print()
    print("🚀 快速使用:")
    print("1. python3 quick_start.py  # 交互式快速开始")
    print("2. python3 example.py      # 运行示例代码")
    print("3. python3 get_similarweb.py  # 运行主程序")
    print()
    print("⚙️  配置要求:")
    print("- 有效的SimilarWeb Pro账户")
    print("- 从浏览器获取的Cookie信息")
    print("- Python 3.6+ 和 requests 库")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        show_help()
    else:
        quick_start()
