# SimilarWeb数据采集器

这是一个用于采集SimilarWeb网站分析数据的Python爬虫工具。

## 功能特性

- 🔍 **网站概览数据采集** - 获取网站基本性能指标
- 📊 **流量数据分析** - 采集详细的流量统计信息  
- 👥 **受众数据获取** - 分析网站访问者特征
- 💾 **多格式数据保存** - 支持JSON和CSV格式导出
- 🔧 **灵活参数配置** - 支持不同国家和时间范围
- 🛡️ **Cookie认证** - 使用Cookie进行身份验证

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 获取Cookie信息

1. 登录 [SimilarWeb Pro](https://pro.similarweb.com)
2. 打开浏览器开发者工具 (F12)
3. 切换到 Network 标签页
4. 刷新页面或进行任意操作
5. 找到任意请求，复制其Cookie信息

### 2. 配置Cookie

编辑 `config.py` 文件，将Cookie信息粘贴到 `SIMILARWEB_COOKIES` 变量中：

```python
SIMILARWEB_COOKIES = """
session_id=your_session_id; user_token=your_token; other_cookies=values
"""
```

### 3. 运行爬虫

```bash
python get_similarweb.py
```

### 4. 编程方式使用

```python
from get_similarweb import SimilarWebSpider

# 创建爬虫实例
spider = SimilarWebSpider(cookies="your_cookies_here")

# 采集单个网站数据
data = spider.collect_all_data("klingai.com", country="999", duration="1m")

# 保存数据
spider.save_to_json(data)
spider.save_to_csv(data)
```

## 支持的参数

### 时间范围 (duration)
- `1m` - 1个月
- `3m` - 3个月  
- `6m` - 6个月
- `12m` - 12个月
- `18m` - 18个月
- `24m` - 24个月

### 国家代码 (country)
- `999` - 全球
- `840` - 美国
- `156` - 中国
- `392` - 日本
- `826` - 英国
- `276` - 德国
- 更多国家代码请参考 `config.py`

## 数据输出

### JSON格式
```json
{
  "domain": "klingai.com",
  "country": "999",
  "duration": "1m",
  "collected_at": "2024-01-01T12:00:00",
  "overview": { ... },
  "traffic": { ... },
  "audience": { ... }
}
```

### CSV格式
- 结构化的表格数据
- 包含所有采集的指标
- 便于Excel等工具分析

## 目录结构

```
S-similarweb/
├── get_similarweb.py  # 主程序
├── config.py          # 配置文件
├── README.md          # 说明文档
└── data/              # 数据保存目录
    ├── *.json         # JSON格式数据
    └── *.csv          # CSV格式数据
```

## 注意事项

⚠️ **重要提醒**：
1. 需要有效的SimilarWeb Pro账户和Cookie
2. 请遵守SimilarWeb的使用条款
3. 建议设置合理的请求间隔，避免过于频繁的请求
4. Cookie可能会过期，需要定期更新

## 错误处理

- 自动重试机制
- 详细的错误日志
- 网络异常处理
- JSON解析错误处理

## 扩展功能

可以根据需要扩展更多SimilarWeb API端点：
- 竞争对手分析
- 关键词数据
- 推荐流量来源
- 社交媒体数据

## 技术支持

如有问题请检查：
1. Cookie是否有效且未过期
2. 网络连接是否正常
3. 目标域名是否正确
4. SimilarWeb账户是否有相应权限
