#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SimilarWeb Pro 爬虫使用示例
"""

from similarweb import SimilarWebProSpider


def run_single_domain():
    """运行单个域名分析"""
    # 目标URL
    url = "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com"
    
    # 创建爬虫实例
    spider = SimilarWebProSpider(
        headless=False,  # 显示浏览器界面，方便调试
        timeout=60000    # 60秒超时
    )
    
    print("开始分析 klingai.com...")
    
    # 运行爬虫
    data = spider.run(url)
    
    if data:
        print("✅ 数据获取成功")
        print(f"页面标题: {data.get('title', 'N/A')}")
        print(f"提取的指标数量: {len(data.get('metrics', {}))}")
        print(f"找到的图表数量: {len(data.get('charts', {}))}")
        print(f"找到的表格数量: {len(data.get('tables', {}))}")
    else:
        print("❌ 数据获取失败")


def run_multiple_domains():
    """运行多个域名分析"""
    domains = [
        "klingai.com",
        "openai.com", 
        "anthropic.com"
    ]
    
    spider = SimilarWebProSpider(headless=False)
    
    for domain in domains:
        print(f"\n{'='*50}")
        print(f"正在分析: {domain}")
        print(f"{'='*50}")
        
        # 构建URL
        url = f"https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key={domain}"
        
        # 运行分析
        data = spider.run(url)
        
        if data:
            print(f"✅ {domain} 分析完成")
        else:
            print(f"❌ {domain} 分析失败")
        
        # 等待一下再处理下一个域名
        import time
        time.sleep(5)


def run_with_custom_params():
    """使用自定义参数运行"""
    # 自定义参数
    domain = "github.com"
    country = "840"  # 美国
    duration = "3m"  # 3个月
    
    # 构建URL
    url = f"https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/{country}/{duration}?webSource=Total&key={domain}"
    
    spider = SimilarWebProSpider(headless=False)
    
    print(f"分析参数:")
    print(f"  域名: {domain}")
    print(f"  国家: {country}")
    print(f"  时间范围: {duration}")
    
    data = spider.run(url)
    
    if data:
        print("✅ 自定义参数分析完成")
    else:
        print("❌ 自定义参数分析失败")


if __name__ == "__main__":
    print("SimilarWeb Pro 爬虫示例")
    print("请选择运行模式:")
    print("1. 单个域名分析 (klingai.com)")
    print("2. 多个域名分析")
    print("3. 自定义参数分析")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        run_single_domain()
    elif choice == "2":
        run_multiple_domains()
    elif choice == "3":
        run_with_custom_params()
    else:
        print("无效选择，运行默认单域名分析...")
        run_single_domain()
