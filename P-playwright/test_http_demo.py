#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP 请求测试演示
使用公共API演示HTTP请求功能，然后展示SimilarWeb API的请求格式
"""

import requests
import json
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def test_public_api():
    """测试公共API以验证HTTP请求功能正常"""
    print("="*60)
    print("测试公共API (验证HTTP请求功能)")
    print("="*60)
    
    # 使用JSONPlaceholder作为测试API
    url = "https://jsonplaceholder.typicode.com/posts/1"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json"
    }
    
    print(f"请求URL: {url}")
    print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 公共API测试成功!")
            print("响应数据:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False


def simulate_similarweb_request():
    """模拟SimilarWeb API请求（展示请求格式）"""
    print("\n" + "="*60)
    print("模拟 SimilarWeb API 请求")
    print("="*60)
    
    # SimilarWeb API URL和参数
    url = "https://pro.similarweb.com/api/WebsiteOverview/getheader"
    params = {
        "keys": "klingai.com",
        "mainDomainOnly": "true", 
        "includeCrossData": "true"
    }
    
    # 完整的请求头（基于你提供的信息）
    headers = {
        "accept": "application/json",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "content-type": "application/json; charset=utf-8",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors", 
        "sec-fetch-site": "same-origin",
        "dnt": "1",
        "priority": "u=1, i",
        "referer": "https://pro.similarweb.com/",
        "x-requested-with": "XMLHttpRequest",
        "x-sw-page": "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com",
        "x-sw-page-view-id": "7b515336-e6e5-409c-951f-82a5e66eaaa0"
        # 注意: 这里缺少Cookie，所以请求会失败
    }
    
    print(f"请求URL: {url}")
    print(f"请求参数: {params}")
    print(f"请求头数量: {len(headers)}")
    print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n📋 完整的请求头信息:")
    for key, value in headers.items():
        print(f"  {key}: {value}")
    
    print(f"\n🔗 完整的请求URL:")
    param_str = "&".join([f"{k}={v}" for k, v in params.items()])
    full_url = f"{url}?{param_str}"
    print(f"  {full_url}")
    
    print(f"\n📝 等效的curl命令:")
    curl_headers = " ".join([f'-H "{k}: {v}"' for k, v in headers.items()])
    print(f"curl -X GET '{full_url}' \\")
    print(f"  {curl_headers}")
    
    # 尝试发送请求（预期会失败，因为没有Cookie）
    print(f"\n🚀 发送请求测试...")
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            try:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
            except:
                print("响应内容:")
                print(response.text[:500])
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print("响应内容:")
            print(response.text[:500])
            
            if response.status_code in [401, 403]:
                print("\n💡 这是预期的结果，因为:")
                print("  - 没有提供有效的Cookie")
                print("  - SimilarWeb Pro需要登录认证")
                print("  - 需要有效的会话Cookie才能访问API")
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误 - 可能是网络问题或需要VPN")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def show_cookie_instructions():
    """显示如何获取Cookie的详细说明"""
    print("\n" + "="*60)
    print("如何获取有效的Cookie")
    print("="*60)
    
    instructions = """
📖 获取SimilarWeb Pro Cookie的步骤:

1. 打开浏览器，访问 https://pro.similarweb.com
2. 登录你的SimilarWeb Pro账户
3. 打开浏览器开发者工具 (按F12或右键->检查)
4. 切换到 "Network" (网络) 标签页
5. 在SimilarWeb页面中进行任何操作（如搜索域名）
6. 在Network标签页中找到对 /api/ 的请求
7. 点击该请求，查看 "Request Headers" (请求头)
8. 复制 "Cookie" 字段的完整值
9. 将Cookie值粘贴到代码中的cookie_value变量

🔍 Cookie示例格式:
_ga=GA1.2.123456789.1234567890; _gid=GA1.2.987654321.0987654321; session_id=abc123def456; auth_token=xyz789...

⚠️ 注意事项:
- Cookie包含敏感信息，不要分享给他人
- Cookie有过期时间，需要定期更新
- 不同的浏览器会话会有不同的Cookie
- 确保复制完整的Cookie字符串，包括所有分号分隔的部分

🛠️ 使用Cookie的代码示例:
```python
cookie_value = "你的完整Cookie值"
tester = SimilarWebAPITester()
result = tester.test_getheader_api("klingai.com", cookie_value)
```
"""
    print(instructions)


def main():
    """主函数"""
    print("SimilarWeb API HTTP 请求测试演示")
    print("="*60)
    
    # 1. 先测试公共API验证HTTP功能正常
    public_api_success = test_public_api()
    
    # 2. 演示SimilarWeb API请求格式
    simulate_similarweb_request()
    
    # 3. 显示获取Cookie的说明
    show_cookie_instructions()
    
    print("\n" + "="*60)
    print("测试演示完成!")
    print("="*60)
    
    if public_api_success:
        print("✅ HTTP请求功能正常")
        print("✅ SimilarWeb API请求格式已展示")
        print("💡 要成功调用SimilarWeb API，请按照说明获取有效的Cookie")
    else:
        print("❌ HTTP请求功能异常，请检查网络连接")


if __name__ == "__main__":
    main()
