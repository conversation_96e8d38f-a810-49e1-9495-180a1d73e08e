#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SimilarWeb API 测试示例
简单的使用示例，展示如何使用 test_http.py 中的 SimilarWebAPITester
"""

from test_http import SimilarWebAPITester


def simple_test():
    """简单测试示例"""
    print("SimilarWeb API 简单测试")
    print("="*50)
    
    # 创建测试实例
    tester = SimilarWebAPITester()
    
    # 测试单个域名（不使用Cookie）
    print("测试不带Cookie的请求（预期会失败，但可以看到请求格式）:")
    result = tester.test_getheader_api("klingai.com")
    
    print(f"\n测试结果: {'成功' if result['success'] else '失败'}")
    print(f"状态码: {result.get('status_code', 'N/A')}")
    
    if not result['success']:
        print("这是预期的结果，因为没有提供有效的Cookie")
        print("要成功访问API，需要:")
        print("1. 在浏览器中登录 SimilarWeb Pro")
        print("2. 从开发者工具中复制完整的Cookie值")
        print("3. 在代码中设置cookie_value参数")


def test_with_cookie():
    """带Cookie的测试示例"""
    print("\n" + "="*50)
    print("带Cookie的测试示例")
    print("="*50)
    
    # 这里需要填入真实的Cookie值
    # 从浏览器开发者工具的Network标签页中复制
    cookie_value = """
    # 在这里粘贴从浏览器复制的完整Cookie值
    # 例如: _ga=GA1.2.123456789.1234567890; _gid=GA1.2.987654321.0987654321; session_id=abc123...
    """
    
    # 如果Cookie值为空或只是注释，跳过测试
    if not cookie_value.strip() or cookie_value.strip().startswith('#'):
        print("⚠️ 未提供Cookie值，跳过此测试")
        print("要运行此测试，请:")
        print("1. 打开浏览器，访问 https://pro.similarweb.com")
        print("2. 登录你的账户")
        print("3. 打开开发者工具 (F12)")
        print("4. 访问任意API请求，在Network标签页中找到请求")
        print("5. 复制Request Headers中的完整Cookie值")
        print("6. 将Cookie值粘贴到上面的cookie_value变量中")
        return
    
    # 创建测试实例
    tester = SimilarWebAPITester()
    
    # 测试单个域名
    result = tester.test_getheader_api("klingai.com", cookie_value.strip())
    
    if result['success']:
        print("✅ 测试成功！")
        print("获取到的数据:")
        if 'data' in result:
            print(f"数据类型: {type(result['data'])}")
            if isinstance(result['data'], dict):
                print(f"数据键: {list(result['data'].keys())}")
    else:
        print("❌ 测试失败")
        print(f"错误: {result.get('error', 'Unknown error')}")


def batch_test():
    """批量测试多个域名"""
    print("\n" + "="*50)
    print("批量测试示例")
    print("="*50)
    
    # 测试域名列表
    domains = [
        "klingai.com",
        "openai.com", 
        "anthropic.com",
        "google.com",
        "github.com"
    ]
    
    # 创建测试实例
    tester = SimilarWebAPITester()
    
    print(f"将测试 {len(domains)} 个域名:")
    for i, domain in enumerate(domains, 1):
        print(f"{i}. {domain}")
    
    print("\n开始批量测试...")
    
    # 执行批量测试（不使用Cookie）
    results = tester.test_with_different_domains(domains)
    
    # 统计结果
    success_count = sum(1 for result in results.values() if result['success'])
    total_count = len(results)
    
    print(f"\n批量测试完成!")
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}/{total_count}")
    
    # 显示详细结果
    print("\n详细结果:")
    for domain, result in results.items():
        status = "✅ 成功" if result['success'] else "❌ 失败"
        status_code = result.get('status_code', 'N/A')
        print(f"  {domain}: {status} (状态码: {status_code})")


if __name__ == "__main__":
    # 运行所有测试示例
    # simple_test()
    test_with_cookie()
    batch_test()
    
    print("\n" + "="*50)
    print("所有测试示例完成!")
    print("="*50)
    
    print("\n💡 提示:")
    print("- 不带Cookie的请求通常会返回401或403错误")
    print("- 要获取真实数据，需要有效的SimilarWeb Pro账户Cookie")
    print("- 请求间隔设置为2秒，避免触发频率限制")
    print("- 所有测试结果会自动保存到JSON文件中")
