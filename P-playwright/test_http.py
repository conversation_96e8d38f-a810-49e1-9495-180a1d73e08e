#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SimilarWeb API GET Request Test
模拟对 SimilarWeb Pro API 的 GET 请求测试
"""

import requests
import json
import time
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class SimilarWebAPITester:
    """SimilarWeb API 测试类"""

    def __init__(self):
        self.base_url = "https://pro.similarweb.com"
        self.session = requests.Session()

    def test_getheader_api(self, domain="klingai.com", cookie_value=None):
        """
        测试 SimilarWeb getheader API

        Args:
            domain: 要查询的域名
            cookie_value: Cookie值（需要有效的登录Cookie）

        Returns:
            API响应结果
        """
        # API URL
        url = f"{self.base_url}/api/WebsiteOverview/getheader"

        # URL参数
        params = {
            "keys": domain,
            "mainDomainOnly": "true",
            "includeCrossData": "true"
        }

        # 请求头 - 根据提供的信息构建
        headers = {
            # 基础请求头
            "accept": "application/json",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "content-type": "application/json; charset=utf-8",

            # 浏览器相关
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',

            # 请求类型
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",

            # 其他
            "dnt": "1",
            "priority": "u=1, i",
            "referer": "https://pro.similarweb.com/",
            "x-requested-with": "XMLHttpRequest",
            "x-sw-page": f"https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key={domain}",
            "x-sw-page-view-id": "7b515336-e6e5-409c-951f-82a5e66eaaa0"
        }

        # 如果提供了Cookie，添加到请求头
        if cookie_value:
            headers["cookie"] = cookie_value

        print("="*60)
        print(f"测试 SimilarWeb API - {domain}")
        print("="*60)
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        try:
            # 发送GET请求
            print("发送请求中...")
            response = self.session.get(
                url=url,
                params=params,
                headers=headers,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )

            # 打印响应信息
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print()

            # 处理响应
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print("✅ 请求成功!")
                    print("响应数据:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "data": json_data,
                        "headers": dict(response.headers)
                    }
                except json.JSONDecodeError:
                    print("⚠️ 响应不是有效的JSON格式")
                    print("响应内容:")
                    print(response.text[:1000])  # 只显示前1000个字符
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "error": "Invalid JSON response",
                        "content": response.text
                    }
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print("响应内容:")
                print(response.text[:1000])
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": f"HTTP {response.status_code}",
                    "content": response.text
                }

        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return {"success": False, "error": "Request timeout"}

        except requests.exceptions.ConnectionError:
            print("❌ 连接错误")
            return {"success": False, "error": "Connection error"}

        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_with_different_domains(self, domains=None, cookie_value=None):
        """
        测试多个域名

        Args:
            domains: 域名列表
            cookie_value: Cookie值
        """
        if domains is None:
            domains = ["klingai.com", "openai.com", "google.com"]

        results = {}

        for domain in domains:
            print(f"\n{'='*20} 测试域名: {domain} {'='*20}")
            result = self.test_getheader_api(domain, cookie_value)
            results[domain] = result

            # 请求间隔，避免频率限制
            time.sleep(2)

        return results

    def save_results(self, results, filename=None):
        """
        保存测试结果到文件

        Args:
            results: 测试结果
            filename: 文件名
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"similarweb_api_test_results_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\n✅ 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")


def main():
    """主函数 - 运行测试"""
    print("SimilarWeb API GET 请求测试")
    print("="*60)

    # 创建测试实例
    tester = SimilarWebAPITester()

    # 注意：这里需要有效的Cookie才能成功访问API
    # 你需要从浏览器中复制完整的Cookie值
    cookie_value = "sgID=2a264eb0-f2a2-4af8-b9ba-1d9dd25bab50; locale=zh-cn; bm_mi=66B08A4FD87A347FAE745E200DFAE6DA~YAAQz2YzuFO/pgeYAQAAtQg9Nhw70usc92lRPF+ZEVne3f52QI5kiEZjRlQgiD0J9St+TdF9Vrf5D/VKEG83CTMbxzVzEYqKu+QQHodsh8z9mwH0G25w96Z01CHsVL1scmLU9IOf9y62Xq52fHFjXf2osg9YZ/YPTtUDstq5oQEQ+dpOCyHGUtDY8h9IdUnwHK+7xsD56awOdGBMGKA2bbFonbndzEQlgAv6mn5oEQWKT0MG/oE+ttvVq3+h7MI5o3rAOoIQvNNl+p6huTm0z+4idusTYoZ+6Zu5Z4rNSaHjLDPs3EGYWfq1kRKA3mEBysrl+zuqv/2m+/080dU=~1; _abck=939149C2BED57FE16C9911B0FD65583C~0~YAAQTiLKF8u4JAiYAQAAjTI9Ng4nAWD0Hh0DhY44CtrH5f6CSOHAB+/e4FItr/mtPbJL7VOhtWPyg49XeJNGSTyvG/NQteVJUFkDUYFiOMmMZnC4A5TOwdmL5kf1z/inh3+Gn645WQQWZH4eBfnmflBFa1xCGuMzKE8XPjqte3xj5oSuSeaMLVKKAUGEdcCPtPP6p17A+pwN4SSkK402g1M4CpCWEwfe0eiTXEuuCB6FFsCbvlvjOOevbkunK3V1GmxCWsRYBRAXjeu/tBcvbaccqr1S5/KxwaKguBnw9q9nX+0+5HRP+Rf/8Oa+lEkfzf0nK9ZBBKhy1RNgUrmQ4GZwChVf/TqizdKnz2Ply6Vl04VOX3BrIzDzNMX4nhEKCIK5E0KF5oxkhf87W/IJa/VN4NcR80rm+1q3enf7tqlYkmVwPlNWt0j/pZ6UkUzil/vVRDmG0BhCGtAJ9VLhpAlSy/1vMpem/Zp11pWNTvlR7nVYQE67km5/OTZba16aQvv51ZAYJSdkrbhYg+z/OBuaFQSwetdakYXBIsTmtrT0fJcNnqGrih7awe0DRVUSYTHKgQJr1iwhaKZ8jc7M/oxqXJnCcrIhjd75RMOxjE5nCQlkmFgufqy0j0HPHKyK4g==~-1~-1~-1; bm_sv=733B2CC74768C2F6B1B0ABC732ED1D1D~YAAQTiLKF824JAiYAQAAukg9Nhyv9fAM7uvzkgArp5eChMqCrAR8nlCVVghd7JrGnmpZ6obSkKwUITUo5ybVA/TU07625xOwqlXp2zwpRgRKxXM4pMjZL3hzYNm5068fI7LuroH6j6mpq/iuyo45Err063I6CuIsxPmm9PbpexLRCNVSSPt7o7E+MaVolVu9BJm4uejOcpnUj9yYE4V0IySXUPfLqkVhXu25Tp1T463HQLVmbwdcz+T2eLtuywofpHNPS7E=~1; ak_bmsc=9E2E89B4A6BA76EF267506E38D51E22B~000000000000000000000000000000~YAAQTiLKF9C4JAiYAQAAL0o9Nhwv0egP+qbGZcfn8xePrgtAogzSnub8LVwokQx4LUXv8oTHXScmiHCS67DXD3quoRO2AmthdubQFpU7K8w2vlr/AvDMywoRGT8Nj/WIN1t1UnEUiMgjfeAIy/ntGNe8R9KzkuWm26JSHc3nd+Odvp9NUi25AIPZGZophnmVOOpfjLHwJPlB+AJ2a3EqL2EN+Liz3YplnomYpU+NpGAYwb5vACpBuuFUE9943fHA4Ulsgnwp/l10+YCfxVoaWfAfVYpQ3pwXOqJc9s2yMj1bevEGCxlMnrugXiye3pHhy3JPz0J22Ml/pEZT80XJQt/AmSvhWW3XjSNOvPexrx38XfeqbxhgcZLF6Cb2Ocz3MFbcl51PTBeIXH7T/MZBX/tY/YyVnxH3A7vfg6dpTv11213vNDSDRLtqznaMS24=; __q_state_9u7uiM39FyWVMWQF=eyJ1dWlkIjoiMDY3NDI5ZjYtN2JlZC00NDViLTgzMWMtNWQ2MWUwYzRjNTk3IiwiY29va2llRG9tYWluIjoic2ltaWxhcndlYi5jb20iLCJhY3RpdmVTZXNzaW9uSWQiOm51bGwsInNjcmlwdElkIjpudWxsLCJtZXNzZW5nZXJFeHBhbmRlZCI6bnVsbCwicHJvbXB0RGlzbWlzc2VkIjpmYWxzZSwiY29udmVyc2F0aW9uSWQiOm51bGx9; RESET_PRO_CACHE=True; .DEVICETOKEN.SIMILARWEB.COM=4XxQjw73ghMfLBJFbtHtuUCSzKegvhih; .SGTOKEN.SIMILARWEB.COM=yIQhD0ZXJf4snf1GtxgN1eVgkVCd2KC6viJdhi9M1wLwP2CSrRSoPwYzxLX4ARkhN7hyci5CsR9AuKECwDB3Q3c8fiGZvdbn_8wWRI7ukcjpp7e2D1bOmHQCpH3shP0NnGTswruNwTMGTMFgV7XQiX8j58Va2OVnzBgqTMIkQitp2GDvTlcRKC6t93xU1VE52cUzqDny_32xjrGodGjftJJawRDECFuazj4ymvNw5fv3fvkwxNeiGpfhosZGm87KCR2t05g9aeu2bCKwq_-LXBUoCF5qDvIrSXdSSB7JJ8O8T-Xva9fSz4tu9NYgofCBaqUJF0DDa5T-o5MbNTCaEeOQPYX5C3x08ron2Y5pHq00sMOGj9Cfvpy_gJ-Ufv7O6e3ILjpXaERIEh1VOCQEwmJNmaFoRsYQqtzrkxsQIRkEy2cXSxY0ReD2-qvHoYUaRblYHGX5VgYwaPU2ur7D4JCFwDZ7FoT6JTU49mSyASuL1RzITfxPTjD-jLW-xrGzecZAP1SxYs2LeCAVSKIMZEmc9Y8XxY2L74IQrlaRGSNO2yuhA0gDrLbaP1aPGxjZ; _sw_pin=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; _sw_pin_ps=0; bm_sz=D248CE27574234811888EF84AECBF534~YAAQliLKF2u9kzOYAQAAJ15JNhxhuOefY6WAuLO/+tFBdRmLMfwELJ70HE3JsQazdudka6UYWeemklgcbPkjXD6hJeoa7ZiwZzXLPDtVeABDmbGCh8kkBrNYnT5ob6dleQhlZnYsFTmy7aI1OxN9OW21eK9TamjLX0WRudslD7xb/LT201xEe1flsj24ZAYI2k1AhRhnmWddF+2zD76IeI4dxbuwBBQRSndwUG98QAxrYOnfOi6sS5I+4/LLUcXfPftiKgIakYzUPOMatGdQwPEVbqETajtClyeD71HNqYaVk/MY8My0a599/uEyu4ZsQ+zm+RRw/0B3OugKvUcRo+9pptmD6M8ENQ+RHJ71gsfyveSs5JMQmuywrBx9QBDPPU1k/H89bpmbin739rybtgiSAk3L4a8UyoS4t8zwfePGokxUyiz7V0tBVsI7fDrphb15BVHZK/AloiLFu3AFhBIyLhG/fWTDdC2tufPoonEycBjWqgbU2kUWO5l1/Kg=~3224121~3617862; aws-waf-token=c5a46a5a-f0db-49d5-afa5-2729b9d0c04e:AgoAkAI4p2kOAAAA:LzKDB8Kch5RB8PhcxUEyZ+iUHSrHsEg8dFCc2C8B1+jf0JhuNdn1vTTH9un8jC3XC3UMYOYS3E8JJytyc2SqZODQc8RhKrqnmK0yhY8BPBLTWyTYlly6yWkhjXtzQ5jhcMyFEJEEq8OVGLcKOYMQio8gvC0uvFqtI1jBjr284CeDdvkeRrV+6m0a9i3ZGcPO181f3fBrlrS9xPwpNGCtnTc7+KIeBhQ/; _dd_s=rum=0&expire=1753258904204"  # 在这里填入你的Cookie值

    # 如果你有Cookie，可以取消注释下面这行并填入实际的Cookie值
    # cookie_value = "your_cookie_value_here"

    if not cookie_value:
        print("⚠️ 警告: 未提供Cookie值，请求可能会失败")
        print("请从浏览器开发者工具中复制完整的Cookie值")
        print()

    # 测试单个域名
    print("1. 测试单个域名 (klingai.com)")
    result = tester.test_getheader_api("klingai.com", cookie_value)

    # 测试多个域名
    print("\n2. 测试多个域名")
    domains = ["klingai.com", "openai.com", "anthropic.com"]
    results = tester.test_with_different_domains(domains, cookie_value)

    # 保存结果
    tester.save_results(results)

    print("\n" + "="*60)
    print("测试完成!")


if __name__ == "__main__":
    main()