# SimilarWeb API GET 请求测试工具

这个工具用于模拟对 SimilarWeb Pro API 的 GET 请求测试，特别是针对 `/api/WebsiteOverview/getheader` 端点。

## 文件说明

- `test_http.py` - 主要的测试类和功能
- `test_http_example.py` - 使用示例和演示
- `test_http_demo.py` - 包含公共API测试和详细说明的演示

## 功能特性

### 1. 完整的HTTP请求模拟
- 精确复制浏览器请求头
- 支持Cookie认证
- 处理SSL证书验证
- 超时和错误处理

### 2. 多种测试模式
- 单个域名测试
- 批量域名测试
- 带Cookie和不带Cookie的测试

### 3. 优化的结果保存
- **详细结果文件**: 包含测试信息、摘要和完整结果
- **原始数据文件**: 仅包含API返回的原始数据
- JSON格式，易于解析和分析

## 使用方法

### 基本使用

```python
from test_http import SimilarWebAPITester

# 创建测试实例
tester = SimilarWebAPITester()

# 测试单个域名（不带Cookie）
result = tester.test_getheader_api("klingai.com")

# 测试单个域名（带Cookie）
cookie_value = "你的完整Cookie值"
result = tester.test_getheader_api("klingai.com", cookie_value)

# 批量测试多个域名
domains = ["klingai.com", "openai.com", "anthropic.com"]
results = tester.test_with_different_domains(domains, cookie_value)

# 保存结果
tester.save_results(results)  # 保存详细结果
tester.save_raw_data(results)  # 保存原始数据
```

### 运行测试

```bash
# 运行主测试
python3 test_http.py

# 运行示例演示
python3 test_http_example.py

# 运行完整演示（包含公共API测试）
python3 test_http_demo.py
```

## 获取Cookie的步骤

1. 打开浏览器，访问 https://pro.similarweb.com
2. 登录你的SimilarWeb Pro账户
3. 打开浏览器开发者工具 (F12)
4. 切换到 "Network" (网络) 标签页
5. 在SimilarWeb页面中进行任何操作（如搜索域名）
6. 在Network标签页中找到对 `/api/` 的请求
7. 点击该请求，查看 "Request Headers" (请求头)
8. 复制 "Cookie" 字段的完整值
9. 将Cookie值粘贴到代码中

## API请求详情

### 请求URL
```
https://pro.similarweb.com/api/WebsiteOverview/getheader
```

### 请求参数
- `keys`: 要查询的域名
- `mainDomainOnly`: "true"
- `includeCrossData`: "true"

### 请求头
包含完整的浏览器请求头，包括：
- User-Agent
- Accept headers
- Security headers (sec-ch-ua, sec-fetch-*)
- SimilarWeb特定头 (x-sw-page, x-sw-page-view-id)
- Cookie (需要有效的登录Cookie)

## 响应数据格式

成功的API响应包含以下信息：
- 域名基本信息（标题、描述、图标）
- 全球排名和分类排名
- 月访问量
- 相关移动应用
- 公司信息（成立年份、员工规模）
- 标签和分类信息

## 结果文件格式

### 详细结果文件 (`similarweb_api_test_results_*.json`)
```json
{
  "test_info": {
    "timestamp": "2025-07-23T18:18:56.938945",
    "total_domains": 3,
    "successful_requests": 2,
    "failed_requests": 1
  },
  "summary": {
    "domain.com": {
      "status": "success",
      "global_ranking": 1234,
      "monthly_visits": 5000000,
      "category": "Technology",
      "title": "Domain Title",
      "description": "Domain description..."
    }
  },
  "detailed_results": {
    // 完整的API响应数据
  }
}
```

### 原始数据文件 (`similarweb_raw_data_*.json`)
```json
{
  "domain.com": {
    "mainDomainName": "domain",
    "globalRanking": 1234,
    "monthlyVisits": 5000000,
    // ... 其他API返回的原始数据
  }
}
```

## 注意事项

1. **Cookie安全**: Cookie包含敏感信息，不要分享给他人
2. **Cookie过期**: Cookie有过期时间，需要定期更新
3. **请求频率**: 代码中设置了2秒的请求间隔，避免触发频率限制
4. **网络连接**: 如果遇到连接错误，可能需要检查网络或VPN设置
5. **账户权限**: 需要有效的SimilarWeb Pro账户才能访问API

## 错误处理

工具会处理以下错误情况：
- 连接错误
- 超时错误
- HTTP错误状态码
- JSON解析错误
- 认证失败

所有错误都会被记录在结果文件中，便于调试和分析。

## 扩展功能

可以轻松扩展工具以支持：
- 其他SimilarWeb API端点
- 不同的请求参数
- 自定义的结果处理
- 数据可视化
- 自动化报告生成
