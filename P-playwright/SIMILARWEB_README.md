# SimilarWeb Pro 爬虫工具

使用 Playwright 模拟访问 SimilarWeb Pro 网站分析平台，获取网站流量和分析数据。

## 功能特性

- 🌐 模拟浏览器访问 SimilarWeb Pro
- 🍪 支持 Cookie 认证
- 📊 自动提取网站分析数据
- 📸 自动截图保存
- 💾 数据导出为 JSON 格式
- 🔄 支持批量域名分析

## 文件说明

- `similarweb.py` - 主要爬虫类
- `run_similarweb.py` - 使用示例脚本
- `similarweb_cookies.json` - Cookie 配置文件模板
- `SIMILARWEB_README.md` - 本说明文件

## 安装依赖

确保已安装 Playwright：

```bash
pip install playwright
playwright install chromium
```

## 使用方法

### 1. 准备 Cookies（重要）

SimilarWeb Pro 需要登录才能访问。请按以下步骤获取 cookies：

1. 在浏览器中登录 https://pro.similarweb.com
2. 打开开发者工具 (F12)
3. 转到 Application/Storage -> Cookies -> https://pro.similarweb.com
4. 复制所有 cookies 到 `similarweb_cookies.json` 文件中

示例格式：
```json
{
    "session_id": "your_session_id",
    "auth_token": "your_auth_token",
    "user_preferences": "your_preferences"
}
```

### 2. 运行脚本

#### 方式一：直接运行主脚本
```bash
python similarweb.py
```

#### 方式二：使用示例脚本
```bash
python run_similarweb.py
```

#### 方式三：在代码中使用
```python
from similarweb import SimilarWebProSpider

# 创建爬虫实例
spider = SimilarWebProSpider(headless=False)

# 目标URL
url = "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com"

# 运行爬虫
data = spider.run(url)
```

## URL 参数说明

SimilarWeb Pro URL 格式：
```
https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/[country]/[duration]?webSource=Total&key=[domain]
```

参数说明：
- `domain`: 要分析的域名 (如: klingai.com)
- `country`: 国家代码 (999=全球, 840=美国, 156=中国)
- `duration`: 时间范围 (1m=1个月, 3m=3个月, 6m=6个月)

## 输出文件

脚本会在以下目录生成文件：

- `screenshots/` - 页面截图
- `html/` - 页面HTML源码
- `results/` - 提取的数据JSON文件
- `downloads/` - 下载的文件

## 数据结构

提取的数据包含：

```json
{
    "extracted_at": "2025-01-23T10:30:00",
    "url": "访问的URL",
    "title": "页面标题",
    "metrics": {
        "metric_0": "指标值1",
        "metric_1": "指标值2"
    },
    "charts": {
        "chart_0": {
            "type": "图表类型",
            "data_attributes": {}
        }
    },
    "tables": {
        "table_0": [
            ["表头1", "表头2"],
            ["数据1", "数据2"]
        ]
    }
}
```

## 注意事项

1. **登录状态**: 必须提供有效的 SimilarWeb Pro cookies
2. **访问频率**: 避免过于频繁的请求，建议请求间隔 3-5 秒
3. **网络稳定**: 确保网络连接稳定，页面加载可能需要较长时间
4. **浏览器版本**: 建议使用最新版本的 Chrome 浏览器

## 故障排除

### 常见问题

1. **页面加载失败**
   - 检查网络连接
   - 确认 cookies 是否有效
   - 尝试增加超时时间

2. **数据提取为空**
   - 检查页面是否完全加载
   - 确认目标元素是否存在
   - 查看截图确认页面状态

3. **Cookie 过期**
   - 重新登录 SimilarWeb Pro
   - 更新 cookies 文件

### 调试模式

设置 `headless=False` 可以看到浏览器操作过程，便于调试：

```python
spider = SimilarWebProSpider(headless=False)
```

## 免责声明

本工具仅用于学习和研究目的。使用时请遵守 SimilarWeb 的服务条款和相关法律法规。
